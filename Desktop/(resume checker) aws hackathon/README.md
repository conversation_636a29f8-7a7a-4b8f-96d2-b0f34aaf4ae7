# Resume-to-Job-Match Analyzer

🏆 **AWS Lambda Hackathon Entry**

## 📊 Project Overview

A serverless application that analyzes how well a resume matches a job description using AWS Lambda, Amazon Bedrock (Claude), and modern web technologies.

### 🚀 Features

- **Resume Upload**: Support for PDF and text files
- **AI-Powered Analysis**: Uses Amazon Bedrock (Claude) for intelligent matching
- **Real-time Results**: Get match percentage, missing skills, and improvement recommendations
- **Modern UI**: Clean, responsive design with Tailwind CSS
- **Serverless Architecture**: Fully serverless using AWS Lambda

### 🛠 Tech Stack

**Frontend:**
- HTML5, CSS3, JavaScript
- Tailwind CSS for styling
- Axios for API requests

**Backend (Serverless):**
- AWS Lambda (Python 3.12)
- Google Gemini API (Primary AI Engine)
- Amazon Bedrock (Claude) - Fallback
- API Gateway
- Amazon S3
- DynamoDB (optional)

### 📁 Project Structure

```
resume-checker/
├── backend/
│   ├── lambda_function.py      # Main Lambda handler
│   ├── requirements.txt        # Python dependencies
│   └── utils/
│       ├── resume_parser.py    # Resume parsing utilities
│       ├── bedrock_client.py   # Bedrock integration
│       └── s3_handler.py       # S3 operations
├── frontend/
│   ├── index.html             # Main UI
│   ├── style.css              # Custom styles
│   ├── script.js              # Frontend logic
│   └── package.json           # Frontend dependencies
├── infrastructure/
│   ├── template.yaml          # SAM template
│   └── deploy.sh              # Deployment script
├── .env.example               # Environment variables
└── README.md                  # This file
```

### 🔧 Prerequisites

1. **AWS Account** with access to:
   - AWS Lambda
   - API Gateway
   - Amazon S3
   - DynamoDB (optional)
   - Amazon Bedrock (Claude) - Optional fallback

2. **Google Gemini API Key** (Provided: `AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk`)

3. **AWS CLI** (we'll help you set this up)

4. **SAM CLI** for deployment

### 🚀 Quick Start

1. **Clone and Setup**
   ```bash
   cd "Desktop/(resume checker) aws hackathon"
   npm install  # Install frontend dependencies
   ```

2. **Configure AWS CLI**
   ```bash
   aws configure
   # Enter your AWS Access Key ID
   # Enter your AWS Secret Access Key
   # Default region: us-east-2 (Chicago)
   # Default output format: json
   ```

3. **Deploy Backend**
   ```bash
   sam build
   sam deploy --guided
   ```

4. **Open Frontend**
   ```bash
   open frontend/index.html
   ```

### 🎯 Target Region
- **Primary**: us-east-2 (Chicago)

### 📝 Environment Variables

Copy `.env.example` to `.env` and configure:
```
AWS_REGION=us-east-2
S3_BUCKET_NAME=resume-checker-uploads-[unique-id]
GEMINI_API_KEY=AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk
USE_GEMINI=true
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
```

### 🔄 Development Workflow

1. Make changes to Lambda function
2. Test locally: `sam local start-api`
3. Deploy: `sam deploy`
4. Test frontend with deployed API

### 📊 API Endpoints

- `POST /analyze` - Analyze resume against job description
- `GET /health` - Health check

### 🎥 Demo

[Demo Video Link - To be added]

### 📄 License

MIT License - See LICENSE file for details

---

**Built for AWS Lambda Hackathon 2024**

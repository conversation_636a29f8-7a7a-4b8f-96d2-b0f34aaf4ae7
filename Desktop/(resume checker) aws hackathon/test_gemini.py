#!/usr/bin/env python3
"""
Test script for Google Gemini API integration
"""

import os
import sys
import json
import requests

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from utils.bedrock_client import BedrockClient

def test_gemini_api():
    """Test the Gemini API integration"""
    
    print("🧪 Testing Google Gemini API Integration...")
    print("=" * 50)
    
    # Set environment variables
    os.environ['GEMINI_API_KEY'] = 'AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk'
    os.environ['USE_GEMINI'] = 'true'
    
    # Sample resume and job description
    sample_resume = """
    <PERSON>
    Software Engineer
    Email: <EMAIL>
    Phone: (*************
    
    EXPERIENCE
    Senior Software Engineer | TechCorp Inc. | 2022 - Present
    • Developed web applications using React and Node.js
    • Led team of 3 developers
    • Implemented CI/CD pipelines
    
    Software Engineer | StartupXYZ | 2020 - 2022
    • Built REST APIs using Python and Flask
    • Worked with PostgreSQL databases
    • Collaborated in agile environment
    
    EDUCATION
    Bachelor of Science in Computer Science
    University of Illinois | 2016 - 2020
    
    SKILLS
    JavaScript, Python, React, Node.js, SQL, Git
    """
    
    sample_job = """
    Senior Full Stack Developer
    TechInnovate Solutions
    
    Requirements:
    • 3+ years of software development experience
    • Proficiency in JavaScript, Python, and React
    • Experience with cloud platforms (AWS, Azure, GCP)
    • Knowledge of databases and API development
    • Bachelor's degree in Computer Science
    
    Responsibilities:
    • Develop scalable web applications
    • Work with cross-functional teams
    • Implement best practices for code quality
    • Mentor junior developers
    """
    
    try:
        # Initialize the client
        client = BedrockClient()
        
        print(f"✅ Client initialized")
        print(f"   - Using Gemini: {client.use_gemini}")
        print(f"   - API Key: {client.gemini_api_key[:20]}...")
        print()
        
        # Perform analysis
        print("🔍 Analyzing resume...")
        result = client.analyze_resume_job_match(sample_resume, sample_job)
        
        print("✅ Analysis completed!")
        print()
        
        # Display results
        print("📊 RESULTS:")
        print("-" * 30)
        print(f"Match Percentage: {result['match_percentage']}%")
        print()
        print(f"Assessment: {result['overall_assessment']}")
        print()
        
        print("Strengths:")
        for strength in result['strengths']:
            print(f"  ✓ {strength}")
        print()
        
        print("Missing Skills:")
        for skill in result['missing_skills']:
            print(f"  ⚠ {skill}")
        print()
        
        print("Recommendations:")
        for rec in result['recommendations']:
            print(f"  💡 {rec}")
        print()
        
        print("🎉 Gemini API integration successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print()
        print("🔄 Falling back to demo data...")
        
        # Test fallback
        try:
            os.environ['USE_GEMINI'] = 'false'
            client = BedrockClient()
            result = client.analyze_resume_job_match(sample_resume, sample_job)
            print(f"✅ Fallback successful - Match: {result['match_percentage']}%")
            return False
        except Exception as fallback_error:
            print(f"❌ Fallback also failed: {str(fallback_error)}")
            return False

def test_direct_gemini_api():
    """Test direct Gemini API call"""
    
    print("\n🔗 Testing Direct Gemini API Call...")
    print("=" * 50)
    
    api_key = 'AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk'
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={api_key}"
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Hello! Can you analyze a resume for me? Just respond with 'Yes, I can help with resume analysis.'"
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 100,
        }
    }
    
    try:
        print("📡 Making direct API call...")
        response = requests.post(url, headers=headers, json=data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        
        if 'candidates' in result and len(result['candidates']) > 0:
            response_text = result['candidates'][0]['content']['parts'][0]['text']
            print(f"✅ API Response: {response_text}")
            return True
        else:
            print(f"❌ Unexpected response format: {result}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Resume Analyzer - Gemini API Test")
    print("=" * 60)
    
    # Test direct API first
    direct_success = test_direct_gemini_api()
    
    # Test integration
    integration_success = test_gemini_api()
    
    print("\n📋 SUMMARY:")
    print("=" * 30)
    print(f"Direct API Test: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"Integration Test: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    if direct_success and integration_success:
        print("\n🎉 All tests passed! Gemini integration is working perfectly.")
    elif direct_success:
        print("\n⚠️  Direct API works, but integration needs debugging.")
    else:
        print("\n❌ API key or network issues detected.")
    
    print("\n🔧 Next steps:")
    print("1. Deploy the updated Lambda function")
    print("2. Test the full application")
    print("3. Verify the frontend integration")

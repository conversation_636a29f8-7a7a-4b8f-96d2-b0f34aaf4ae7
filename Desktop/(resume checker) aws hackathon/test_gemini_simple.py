#!/usr/bin/env python3
"""
Simple test script for Google Gemini API
"""

import requests
import json

def test_gemini_api():
    """Test the Gemini API with resume analysis"""
    
    print("🧪 Testing Google Gemini API...")
    print("=" * 50)
    
    api_key = 'AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk'
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={api_key}"
    
    # Sample resume and job for analysis
    prompt = """You are an expert HR analyst. Analyze how well this resume matches the job description.

RESUME:
<PERSON>
Software Engineer
Email: <EMAIL>

EXPERIENCE:
Senior Software Engineer | TechCorp Inc. | 2022 - Present
• Developed web applications using React and Node.js
• Led team of 3 developers

SKILLS: JavaScript, Python, React, Node.js, SQL

JOB DESCRIPTION:
Senior Full Stack Developer
Requirements:
• 3+ years of software development experience
• Proficiency in JavaScript, Python, and React
• Experience with cloud platforms (AWS, Azure, GCP)

Please provide a JSON response with:
{
    "match_percentage": <number 0-100>,
    "overall_assessment": "<brief summary>",
    "strengths": ["<strength1>", "<strength2>"],
    "missing_skills": ["<skill1>", "<skill2>"],
    "recommendations": ["<rec1>", "<rec2>"]
}"""
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "topK": 1,
            "topP": 1,
            "maxOutputTokens": 1024,
        }
    }
    
    try:
        print("📡 Making API call to Gemini...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'candidates' in result and len(result['candidates']) > 0:
                response_text = result['candidates'][0]['content']['parts'][0]['text']
                
                print("✅ API call successful!")
                print("\n📄 Raw Response:")
                print("-" * 30)
                print(response_text)
                
                # Try to parse JSON from response
                try:
                    # Look for JSON in the response
                    start_idx = response_text.find('{')
                    end_idx = response_text.rfind('}') + 1
                    
                    if start_idx != -1 and end_idx != -1:
                        json_str = response_text[start_idx:end_idx]
                        analysis = json.loads(json_str)
                        
                        print("\n📊 Parsed Analysis:")
                        print("-" * 30)
                        print(f"Match Percentage: {analysis.get('match_percentage', 'N/A')}%")
                        print(f"Assessment: {analysis.get('overall_assessment', 'N/A')}")
                        
                        strengths = analysis.get('strengths', [])
                        if strengths:
                            print("\nStrengths:")
                            for strength in strengths:
                                print(f"  ✓ {strength}")
                        
                        missing = analysis.get('missing_skills', [])
                        if missing:
                            print("\nMissing Skills:")
                            for skill in missing:
                                print(f"  ⚠ {skill}")
                        
                        recommendations = analysis.get('recommendations', [])
                        if recommendations:
                            print("\nRecommendations:")
                            for rec in recommendations:
                                print(f"  💡 {rec}")
                        
                        print("\n🎉 Gemini API integration successful!")
                        return True
                    else:
                        print("⚠️  Response doesn't contain valid JSON")
                        return True  # API works, just format issue
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON parsing failed: {e}")
                    print("But API call was successful!")
                    return True
                    
            else:
                print(f"❌ Unexpected response format: {result}")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_simple_gemini():
    """Simple test to verify API key works"""
    
    print("\n🔗 Testing Simple Gemini API Call...")
    print("=" * 50)
    
    api_key = 'AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk'
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={api_key}"
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Hello! Please respond with 'Gemini API is working!' if you can see this message."
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "maxOutputTokens": 50,
        }
    }
    
    try:
        print("📡 Making simple API call...")
        response = requests.post(url, headers=headers, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if 'candidates' in result and len(result['candidates']) > 0:
                response_text = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ Response: {response_text}")
                return True
            else:
                print(f"❌ Unexpected response: {result}")
                return False
        else:
            print(f"❌ Status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Gemini API Test Suite")
    print("=" * 60)
    
    # Test 1: Simple API call
    simple_success = test_simple_gemini()
    
    # Test 2: Resume analysis
    analysis_success = test_gemini_api()
    
    print("\n📋 SUMMARY:")
    print("=" * 30)
    print(f"Simple API Test: {'✅ PASS' if simple_success else '❌ FAIL'}")
    print(f"Resume Analysis Test: {'✅ PASS' if analysis_success else '❌ FAIL'}")
    
    if simple_success and analysis_success:
        print("\n🎉 All tests passed! Your Gemini API key is working perfectly.")
        print("✅ Ready to integrate with the Lambda function!")
    elif simple_success:
        print("\n⚠️  API key works, but resume analysis needs refinement.")
        print("✅ Basic integration is ready!")
    else:
        print("\n❌ API key or network issues detected.")
        print("🔧 Please check your API key and internet connection.")
    
    print("\n🔧 Next steps:")
    print("1. Update the Lambda function with Gemini integration")
    print("2. Deploy the updated backend")
    print("3. Test the full application")

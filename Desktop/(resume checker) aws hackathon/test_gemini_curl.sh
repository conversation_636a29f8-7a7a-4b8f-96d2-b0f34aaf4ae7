#!/bin/bash

# Test Google Gemini API with curl
echo "🧪 Testing Google Gemini API with curl..."
echo "================================================"

API_KEY="AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk"
URL="https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${API_KEY}"

echo "📡 Making API call to Gemini..."
echo "URL: ${URL}"
echo ""

# Test 1: Simple hello test
echo "Test 1: Simple Hello Test"
echo "-------------------------"

curl -X POST "${URL}" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [{
      "parts": [{
        "text": "Hello! Please respond with \"Gemini API is working!\" if you can see this message."
      }]
    }],
    "generationConfig": {
      "temperature": 0.1,
      "maxOutputTokens": 50
    }
  }' 2>/dev/null | python3 -m json.tool

echo ""
echo "=================================="
echo ""

# Test 2: Resume analysis test
echo "Test 2: Resume Analysis Test"
echo "-----------------------------"

curl -X POST "${URL}" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [{
      "parts": [{
        "text": "You are an expert HR analyst. Analyze this resume for a software engineer position:\n\nRESUME:\nJohn Smith\nSoftware Engineer\nSkills: JavaScript, Python, React\nExperience: 3 years web development\n\nJOB:\nSenior Developer\nRequirements: 5+ years experience, JavaScript, Python, AWS\n\nProvide a brief analysis with match percentage."
      }]
    }],
    "generationConfig": {
      "temperature": 0.1,
      "maxOutputTokens": 200
    }
  }' 2>/dev/null | python3 -m json.tool

echo ""
echo "🎉 Gemini API test completed!"
echo ""
echo "If you see responses above, your API key is working correctly!"
echo "Next step: Deploy the Lambda function with Gemini integration."

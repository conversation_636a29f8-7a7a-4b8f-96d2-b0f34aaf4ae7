# AWS Configuration
AWS_REGION=us-east-2
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# S3 Configuration
S3_BUCKET_NAME=resume-checker-uploads-dev-123456789

# AI Configuration
# Amazon Bedrock Configuration (fallback)
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Google Gemini Configuration (primary)
GEMINI_API_KEY=AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk
USE_GEMINI=true

# API Gateway Configuration (will be populated after deployment)
API_GATEWAY_URL=https://your-api-id.execute-api.us-east-2.amazonaws.com/dev

# DynamoDB Configuration (optional)
DYNAMODB_TABLE_NAME=resume-analytics-dev

# Application Configuration
ENVIRONMENT=dev
LOG_LEVEL=INFO

# Frontend Configuration
FRONTEND_URL=http://localhost:8000

# Security Configuration
CORS_ALLOWED_ORIGINS=*
MAX_FILE_SIZE_MB=5

# Optional: Analytics and Monitoring
ENABLE_ANALYTICS=true
ENABLE_DETAILED_LOGGING=true

# Optional: Rate Limiting
RATE_LIMIT_PER_MINUTE=10
RATE_LIMIT_PER_HOUR=100

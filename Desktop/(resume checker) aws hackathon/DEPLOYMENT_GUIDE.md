# 🚀 Complete Deployment Guide

## 📋 **Current Status**

✅ **Frontend**: Running on http://localhost:8000  
✅ **Gemini API**: Fully functional and tested  
✅ **AWS Integration**: Ready for deployment  
✅ **Document Analysis**: Enhanced with Textract & Comprehend  

---

## 🎯 **Quick Start (Demo Mode)**

The application is **currently running in demo mode** and fully functional:

### **1. Access the Application**
- **URL**: http://localhost:8000
- **Status**: ✅ Live and running
- **Features**: All frontend features working with demo data

### **2. Test the Application**
1. **Upload Resume**: Try uploading a PDF or paste resume text
2. **Add Job Description**: Use the sample job description or paste your own
3. **Analyze**: Click "Analyze Resume" to see AI-powered insights
4. **View Results**: See match percentage, strengths, missing skills, and recommendations

---

## 🔧 **AWS Deployment Setup**

To deploy the full AWS backend with <PERSON>rac<PERSON>, Comprehend, and Lambda:

### **Step 1: AWS Account Setup**

```bash
# 1. Create AWS Account (if needed)
# Go to https://aws.amazon.com/ and sign up

# 2. Create IAM User
# Go to AWS Console → IAM → Users → Create User
# User name: resume-analyzer-dev
# Attach policy: AdministratorAccess (for hackathon)

# 3. Create Access Keys
# Go to IAM → Users → your-user → Security credentials
# Create access key → Command Line Interface (CLI)
# Download the CSV file
```

### **Step 2: Configure AWS CLI**

```bash
# Configure AWS credentials
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin
aws configure

# Enter when prompted:
# AWS Access Key ID: [Your access key from CSV]
# AWS Secret Access Key: [Your secret key from CSV]
# Default region name: us-east-2
# Default output format: json

# Test configuration
aws sts get-caller-identity
```

### **Step 3: Install SAM CLI**

```bash
# Install SAM CLI for deployment
brew tap aws/tap
brew install aws-sam-cli

# Or download from: https://github.com/aws/aws-sam-cli/releases

# Verify installation
sam --version
```

### **Step 4: Deploy the Application**

```bash
# Build the application
sam build

# Deploy with guided setup
sam deploy --guided

# When prompted, enter:
# Stack Name: resume-analyzer-stack
# AWS Region: us-east-2
# Parameter Environment: dev
# Confirm changes before deploy: Y
# Allow SAM CLI IAM role creation: Y
# Save parameters to samconfig.toml: Y
```

### **Step 5: Enable Amazon Bedrock (Optional)**

```bash
# Go to AWS Console → Amazon Bedrock
# Region: us-east-2 (Ohio)
# Navigate to "Model access" in left sidebar
# Request access to: Anthropic Claude 3 Sonnet
# Usually approved in 1-5 minutes
```

---

## 🧪 **Testing the Deployment**

### **Test 1: Lambda Functions**
```bash
# Test main function
aws lambda invoke \
  --function-name resume-analyzer-dev \
  --payload '{"httpMethod":"GET","path":"/health"}' \
  response.json

cat response.json
```

### **Test 2: API Gateway**
```bash
# Get API URL from deployment output
API_URL="https://your-api-id.execute-api.us-east-2.amazonaws.com/dev"

# Test health endpoint
curl "${API_URL}/health"
```

### **Test 3: Document Analysis**
```bash
# Test with sample resume
curl -X POST "${API_URL}/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "resume_text": "John Smith, Software Engineer, 3 years experience",
    "job_description": "Looking for Senior Developer with 5+ years experience"
  }'
```

---

## 🎨 **Frontend Configuration**

### **Update API Endpoint**
```bash
# Get your API Gateway URL
API_URL=$(aws cloudformation describe-stacks \
  --stack-name resume-analyzer-stack \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
  --output text)

# Update frontend configuration
sed -i.bak "s|API_BASE_URL: '.*'|API_BASE_URL: '$API_URL'|g" frontend/script.js
```

---

## 📊 **Monitoring & Debugging**

### **CloudWatch Logs**
```bash
# View Lambda logs
sam logs -n ResumeAnalyzerFunction --stack-name resume-analyzer-stack --tail

# View specific log group
aws logs describe-log-groups --log-group-name-prefix /aws/lambda/resume
```

### **Check Service Status**
```bash
# Check Lambda functions
aws lambda list-functions --query 'Functions[?contains(FunctionName, `resume`)]'

# Check API Gateway
aws apigateway get-rest-apis --query 'items[?name==`resume-analyzer-api`]'

# Check S3 bucket
aws s3 ls | grep resume-uploads

# Check DynamoDB table
aws dynamodb list-tables | grep resume-analytics
```

---

## 🔍 **AWS Services Verification**

### **Amazon Textract**
```bash
# Test Textract access
aws textract detect-document-text \
  --document '{"Bytes":"'$(base64 -i test_data/sample_resume.txt)'"}'
```

### **Amazon Comprehend**
```bash
# Test Comprehend access
aws comprehend detect-entities \
  --text "John Smith is a software engineer at TechCorp Inc." \
  --language-code en
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Issue 1: AWS Credentials**
```bash
# Error: Unable to locate credentials
# Solution: Run aws configure with your access keys
aws configure
```

#### **Issue 2: Bedrock Access Denied**
```bash
# Error: AccessDeniedException for Bedrock
# Solution: Request model access in Bedrock console
# Go to Bedrock → Model access → Request access
```

#### **Issue 3: Lambda Timeout**
```bash
# Error: Task timed out after 30.00 seconds
# Solution: Increase timeout in template.yaml
Timeout: 60
```

#### **Issue 4: CORS Errors**
```bash
# Error: CORS policy blocks request
# Solution: Verify CORS settings in template.yaml
```

---

## 💰 **Cost Monitoring**

### **Expected Costs (Per 1000 Analyses)**
- **Lambda**: $0.20
- **API Gateway**: $3.50
- **Textract**: $1.50
- **Comprehend**: $0.10
- **S3**: $0.02
- **DynamoDB**: $0.25
- **Total**: ~$5.57 per 1000 analyses

### **Set Up Billing Alerts**
```bash
# Create budget alert
aws budgets create-budget \
  --account-id $(aws sts get-caller-identity --query Account --output text) \
  --budget file://budget.json
```

---

## 🎉 **Success Verification**

Your deployment is successful when:

✅ **All Lambda functions** deploy without errors  
✅ **API Gateway** returns 200 for /health endpoint  
✅ **S3 bucket** is created and accessible  
✅ **DynamoDB table** is created with proper schema  
✅ **Textract/Comprehend** permissions are working  
✅ **Frontend** connects to API successfully  
✅ **End-to-end analysis** works with real documents  

---

## 🏆 **Production Checklist**

Before going live:

- [ ] AWS credentials configured
- [ ] All services deployed successfully
- [ ] Bedrock model access approved
- [ ] API endpoints tested
- [ ] Frontend updated with API URL
- [ ] Error handling verified
- [ ] Monitoring set up
- [ ] Cost alerts configured
- [ ] Security review completed

---

## 🚀 **Next Steps**

1. **Configure AWS credentials** using the guide above
2. **Deploy the backend** with `sam deploy --guided`
3. **Update frontend** with your API Gateway URL
4. **Test all features** with real documents
5. **Monitor performance** in CloudWatch
6. **Submit for hackathon** with full AWS integration!

**Your Resume-to-Job-Match Analyzer is ready to showcase the full power of AWS! 🎯**

AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Resume-to-Job-Match Analyzer - AWS Lambda Hackathon Entry

# Global configuration
Globals:
  Function:
    Timeout: 30
    MemorySize: 512
    Runtime: python3.12
    Environment:
      Variables:
        AWS_REGION: us-east-2
        BEDROCK_MODEL_ID: anthropic.claude-3-sonnet-20240229-v1:0
        GEMINI_API_KEY: AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk
        USE_GEMINI: true

# Parameters
Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name
  
  S3BucketName:
    Type: String
    Default: resume-checker-uploads
    Description: S3 bucket name for file uploads (will be made unique)

# Resources
Resources:
  # Lambda Function
  ResumeAnalyzerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'resume-analyzer-${Environment}'
      CodeUri: backend/
      Handler: lambda_function.lambda_handler
      Description: Analyzes resume-to-job match using Amazon Bedrock
      Environment:
        Variables:
          S3_BUCKET_NAME: !Ref ResumeUploadsBucket
          ANALYTICS_TABLE_NAME: !Ref AnalyticsTable
          GEMINI_API_KEY: AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk
          USE_GEMINI: true
      Policies:
        - S3ReadWritePolicy:
            BucketName: !Ref ResumeUploadsBucket
        - DynamoDBCrudPolicy:
            TableName: !Ref AnalyticsTable
        - Statement:
          - Effect: Allow
            Action:
              - bedrock:InvokeModel
            Resource: !Sub 'arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0'
        - CloudWatchLogsFullAccess
      Events:
        AnalyzeApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /analyze
            Method: post
        HealthCheckApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /health
            Method: get
        OptionsApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /{proxy+}
            Method: options

  # API Gateway
  ResumeAnalyzerApi:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub 'resume-analyzer-api-${Environment}'
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
        MaxAge: "'600'"
      DefinitionBody:
        swagger: '2.0'
        info:
          title: Resume Analyzer API
          version: '1.0'
        paths:
          /health:
            get:
              x-amazon-apigateway-integration:
                type: aws_proxy
                httpMethod: POST
                uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ResumeAnalyzerFunction.Arn}/invocations'
              responses:
                '200':
                  description: Health check response
                  headers:
                    Access-Control-Allow-Origin:
                      type: string
          /analyze:
            post:
              x-amazon-apigateway-integration:
                type: aws_proxy
                httpMethod: POST
                uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ResumeAnalyzerFunction.Arn}/invocations'
              responses:
                '200':
                  description: Analysis result
                  headers:
                    Access-Control-Allow-Origin:
                      type: string
            options:
              x-amazon-apigateway-integration:
                type: aws_proxy
                httpMethod: POST
                uri: !Sub 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ResumeAnalyzerFunction.Arn}/invocations'
              responses:
                '200':
                  description: CORS preflight response
                  headers:
                    Access-Control-Allow-Origin:
                      type: string
                    Access-Control-Allow-Methods:
                      type: string
                    Access-Control-Allow-Headers:
                      type: string

  # S3 Bucket for file uploads
  ResumeUploadsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '${S3BucketName}-${Environment}-${AWS::AccountId}'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldFiles
            Status: Enabled
            ExpirationInDays: 30
            NoncurrentVersionExpirationInDays: 7
      VersioningConfiguration:
        Status: Enabled

  # CloudWatch Log Group
  ResumeAnalyzerLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/resume-analyzer-${Environment}'
      RetentionInDays: 14

  # DynamoDB table for analytics (Core AWS Service)
  AnalyticsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub 'resume-analytics-${Environment}'
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: analysis_id
          AttributeType: S
        - AttributeName: timestamp
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: analysis_id
          KeyType: HASH
        - AttributeName: timestamp
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: UserAnalysisIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: timestamp
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  # EventBridge Rule for scheduled analytics (Additional Lambda Trigger)
  AnalyticsScheduleRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub 'resume-analytics-schedule-${Environment}'
      Description: 'Trigger analytics processing every hour'
      ScheduleExpression: 'rate(1 hour)'
      State: ENABLED
      Targets:
        - Arn: !GetAtt AnalyticsProcessorFunction.Arn
          Id: 'AnalyticsProcessorTarget'

  # Additional Lambda Function for Analytics Processing
  AnalyticsProcessorFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'resume-analytics-processor-${Environment}'
      CodeUri: backend/
      Handler: analytics_processor.lambda_handler
      Description: Processes analytics data from DynamoDB
      Environment:
        Variables:
          ANALYTICS_TABLE_NAME: !Ref AnalyticsTable
          S3_BUCKET_NAME: !Ref ResumeUploadsBucket
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref AnalyticsTable
        - S3ReadPolicy:
            BucketName: !Ref ResumeUploadsBucket
        - CloudWatchLogsFullAccess
      Events:
        ScheduledAnalytics:
          Type: Schedule
          Properties:
            Schedule: 'rate(1 hour)'
            Description: 'Process analytics every hour'

  # S3 Event Trigger for File Processing
  FileProcessorFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'resume-file-processor-${Environment}'
      CodeUri: backend/
      Handler: file_processor.lambda_handler
      Description: Processes uploaded files from S3
      Environment:
        Variables:
          ANALYTICS_TABLE_NAME: !Ref AnalyticsTable
      Policies:
        - S3ReadPolicy:
            BucketName: !Ref ResumeUploadsBucket
        - DynamoDBWritePolicy:
            TableName: !Ref AnalyticsTable
        - CloudWatchLogsFullAccess
      Events:
        S3FileUpload:
          Type: S3
          Properties:
            Bucket: !Ref ResumeUploadsBucket
            Events: s3:ObjectCreated:*
            Filter:
              S3Key:
                Rules:
                  - Name: prefix
                    Value: resumes/
                  - Name: suffix
                    Value: .pdf

# Outputs
Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL
    Value: !Sub 'https://${ResumeAnalyzerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}'
    Export:
      Name: !Sub '${AWS::StackName}-ApiUrl'

  LambdaFunctionArn:
    Description: Lambda function ARN
    Value: !GetAtt ResumeAnalyzerFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-LambdaArn'

  S3BucketName:
    Description: S3 bucket name for uploads
    Value: !Ref ResumeUploadsBucket
    Export:
      Name: !Sub '${AWS::StackName}-S3Bucket'

  DynamoDBTableName:
    Description: DynamoDB table name for analytics
    Value: !Ref AnalyticsTable
    Export:
      Name: !Sub '${AWS::StackName}-DynamoTable'

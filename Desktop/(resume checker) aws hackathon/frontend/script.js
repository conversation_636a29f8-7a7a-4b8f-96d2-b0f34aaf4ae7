// Resume Match Analyzer - Frontend JavaScript

// Configuration
const CONFIG = {
    // Update this with your actual API Gateway URL after deployment
    API_BASE_URL: 'https://your-api-gateway-url.execute-api.us-east-2.amazonaws.com/dev',
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_FILE_TYPES: ['pdf', 'docx', 'txt']
};

// Global state
let currentFile = null;

// DOM elements
const elements = {
    form: document.getElementById('analysisForm'),
    dropZone: document.getElementById('dropZone'),
    fileInput: document.getElementById('resumeFile'),
    fileInfo: document.getElementById('fileInfo'),
    fileName: document.getElementById('fileName'),
    fileSize: document.getElementById('fileSize'),
    removeFileBtn: document.getElementById('removeFile'),
    resumeText: document.getElementById('resumeText'),
    jobDescription: document.getElementById('jobDescription'),
    analyzeBtn: document.getElementById('analyzeBtn'),
    analyzeText: document.getElementById('analyzeText'),
    loadingState: document.getElementById('loadingState'),
    resultsSection: document.getElementById('resultsSection'),
    errorState: document.getElementById('errorState'),
    errorMessage: document.getElementById('errorMessage'),
    newAnalysisBtn: document.getElementById('newAnalysisBtn'),

    // New UI elements
    fileTab: document.getElementById('fileTab'),
    textTab: document.getElementById('textTab'),
    sampleTab: document.getElementById('sampleTab'),
    fileUploadTab: document.getElementById('fileUploadTab'),
    textInputTab: document.getElementById('textInputTab'),
    sampleResumeTab: document.getElementById('sampleResumeTab'),
    loadSampleBtn: document.getElementById('loadSampleBtn'),
    loadSampleJobBtn: document.getElementById('loadSampleJobBtn'),
    clearJobBtn: document.getElementById('clearJobBtn'),
    textCounter: document.getElementById('textCounter'),
    jobCounter: document.getElementById('jobCounter'),
    resumeStatus: document.getElementById('resumeStatus'),
    jobStatus: document.getElementById('jobStatus'),

    // Progress steps
    step1: document.getElementById('step1'),
    step2: document.getElementById('step2'),
    step3: document.getElementById('step3')
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    checkApiConnection();
});

function initializeEventListeners() {
    // Tab switching
    elements.fileTab.addEventListener('click', () => switchTab('file'));
    elements.textTab.addEventListener('click', () => switchTab('text'));
    elements.sampleTab.addEventListener('click', () => switchTab('sample'));

    // File upload events
    elements.dropZone.addEventListener('click', () => elements.fileInput.click());
    elements.dropZone.addEventListener('dragover', handleDragOver);
    elements.dropZone.addEventListener('dragleave', handleDragLeave);
    elements.dropZone.addEventListener('drop', handleFileDrop);
    elements.fileInput.addEventListener('change', handleFileSelect);
    elements.removeFileBtn.addEventListener('click', removeFile);

    // Sample data loading
    elements.loadSampleBtn.addEventListener('click', loadSampleResume);
    elements.loadSampleJobBtn.addEventListener('click', loadSampleJob);
    elements.clearJobBtn.addEventListener('click', clearJobDescription);

    // Form submission
    elements.form.addEventListener('submit', handleFormSubmit);

    // New analysis button
    elements.newAnalysisBtn.addEventListener('click', resetForm);

    // Results action buttons
    document.getElementById('downloadReportBtn')?.addEventListener('click', downloadReport);
    document.getElementById('shareResultsBtn')?.addEventListener('click', shareResults);

    // Feedback stars
    document.querySelectorAll('.feedback-star').forEach(star => {
        star.addEventListener('click', handleFeedback);
        star.addEventListener('mouseenter', highlightStars);
        star.addEventListener('mouseleave', resetStars);
    });

    // Text counters
    elements.resumeText.addEventListener('input', updateTextCounter);
    elements.jobDescription.addEventListener('input', updateJobCounter);

    // Form validation
    elements.resumeText.addEventListener('input', validateFormInputs);
    elements.jobDescription.addEventListener('input', validateFormInputs);
    elements.fileInput.addEventListener('change', validateFormInputs);

    // Clear resume text when file is uploaded and vice versa
    elements.fileInput.addEventListener('change', () => {
        if (elements.fileInput.files.length > 0) {
            elements.resumeText.value = '';
            updateTextCounter();
        }
    });

    elements.resumeText.addEventListener('input', () => {
        if (elements.resumeText.value.trim()) {
            removeFile();
        }
    });
}

async function checkApiConnection() {
    try {
        const response = await axios.get(`${CONFIG.API_BASE_URL}/health`);
        console.log('API connection successful:', response.data);
    } catch (error) {
        console.warn('API connection failed. Using demo mode.');
        // In demo mode, we'll simulate responses
    }
}

// File handling functions
function handleDragOver(e) {
    e.preventDefault();
    elements.dropZone.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    elements.dropZone.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    elements.dropZone.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFile(file) {
    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
        showError(validation.message);
        return;
    }

    currentFile = file;
    showFileInfo(file);
    hideError();
}

function validateFile(file) {
    // Check file size
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        return {
            valid: false,
            message: 'File size must be less than 5MB'
        };
    }

    // Check file type
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!CONFIG.ALLOWED_FILE_TYPES.includes(fileExtension)) {
        return {
            valid: false,
            message: 'Only PDF, DOCX, and TXT files are supported'
        };
    }

    return { valid: true };
}

function showFileInfo(file) {
    elements.fileName.textContent = file.name;
    elements.fileSize.textContent = formatFileSize(file.size);
    elements.fileInfo.classList.remove('hidden');
    updateResumeStatus(true);
}

function removeFile() {
    currentFile = null;
    elements.fileInput.value = '';
    elements.fileInfo.classList.add('hidden');
    updateResumeStatus(false);
    validateFormInputs();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission and analysis
async function handleFormSubmit(e) {
    e.preventDefault();
    
    // Validate form
    const validation = validateForm();
    if (!validation.valid) {
        showError(validation.message);
        return;
    }

    try {
        showLoading();
        hideError();
        
        const analysisData = await prepareAnalysisData();
        const result = await performAnalysis(analysisData);
        
        displayResults(result);
        
    } catch (error) {
        console.error('Analysis failed:', error);
        showError(error.message || 'Analysis failed. Please try again.');
    } finally {
        hideLoading();
    }
}

function validateForm() {
    const hasFile = currentFile !== null;
    const hasText = elements.resumeText.value.trim() !== '';
    const hasJobDescription = elements.jobDescription.value.trim() !== '';

    if (!hasFile && !hasText) {
        return {
            valid: false,
            message: 'Please upload a resume file or paste resume text'
        };
    }

    if (!hasJobDescription) {
        return {
            valid: false,
            message: 'Please provide a job description'
        };
    }

    return { valid: true };
}

async function prepareAnalysisData() {
    const data = {
        job_description: elements.jobDescription.value.trim()
    };

    if (currentFile) {
        // Convert file to base64
        const fileBase64 = await fileToBase64(currentFile);
        data.resume_file = fileBase64;
        data.file_type = currentFile.name.split('.').pop().toLowerCase();
    } else {
        data.resume_text = elements.resumeText.value.trim();
    }

    return data;
}

function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove the data:application/pdf;base64, prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = error => reject(error);
    });
}

async function performAnalysis(data) {
    try {
        const response = await axios.post(`${CONFIG.API_BASE_URL}/analyze`, data, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 second timeout
        });

        if (response.data.success) {
            return response.data.analysis;
        } else {
            throw new Error(response.data.error || 'Analysis failed');
        }
    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            throw new Error('Analysis timed out. Please try again.');
        } else if (error.response) {
            throw new Error(error.response.data.error || 'Server error occurred');
        } else {
            // Fallback to demo mode for development
            console.warn('Using demo mode');
            return getDemoAnalysis();
        }
    }
}

function getDemoAnalysis() {
    // Demo data for development/testing
    return {
        match_percentage: 75,
        overall_assessment: "Your resume shows a strong match for this position with relevant experience and skills. There are a few areas where you could strengthen your profile to become an even better candidate.",
        strengths: [
            "Strong technical background in required programming languages",
            "Relevant work experience in similar industry",
            "Good educational background matching job requirements"
        ],
        missing_skills: [
            "Cloud computing experience (AWS/Azure)",
            "Project management certification",
            "Advanced data analysis skills"
        ],
        recommendations: [
            "Consider obtaining AWS certification to strengthen cloud skills",
            "Highlight any project management experience more prominently",
            "Add specific examples of data analysis projects"
        ],
        keyword_matches: {
            matched: ["JavaScript", "Python", "React", "Node.js"],
            missing: ["AWS", "Docker", "Kubernetes", "CI/CD"]
        },
        experience_analysis: {
            years_required: "3-5 years",
            years_candidate: "4 years",
            meets_requirement: true
        },
        education_analysis: {
            required: "Bachelor's degree in Computer Science or related field",
            candidate: "Bachelor's in Computer Science",
            meets_requirement: true
        }
    };
}

// Results display functions (moved to enhanced version below)

function updateMatchScore(percentage) {
    const circle = document.getElementById('matchScoreCircle');
    const percentageElement = document.getElementById('matchPercentage');

    // Animate the percentage text
    animateNumber(percentageElement, 0, percentage, 1500);

    // Animate the circle
    const circumference = 2 * Math.PI * 15.9155;
    const offset = circumference - (percentage / 100) * circumference;

    setTimeout(() => {
        circle.style.strokeDasharray = `${circumference}, ${circumference}`;
        circle.style.strokeDashoffset = offset;
    }, 100);

    // Update circle color based on score
    if (percentage >= 80) {
        circle.classList.remove('text-blue-600', 'text-yellow-500');
        circle.classList.add('text-green-600');
    } else if (percentage >= 60) {
        circle.classList.remove('text-blue-600', 'text-green-600');
        circle.classList.add('text-yellow-500');
    } else {
        circle.classList.remove('text-green-600', 'text-yellow-500');
        circle.classList.add('text-red-600');
    }
}

function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.round(start + (end - start) * progress);
        element.textContent = `${current}%`;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

function displayList(containerId, items, itemClass) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';

    items.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = `${itemClass} list-item p-3 rounded-lg`;
        li.style.animationDelay = `${index * 0.1}s`;

        const icon = getIconForItemType(itemClass);
        li.innerHTML = `
            <div class="flex items-start">
                <i class="${icon} mt-1 mr-3 flex-shrink-0"></i>
                <span class="text-gray-800">${item}</span>
            </div>
        `;

        container.appendChild(li);
    });
}

function getIconForItemType(itemClass) {
    switch (itemClass) {
        case 'strength-item':
            return 'fas fa-check text-green-600';
        case 'missing-skill-item':
            return 'fas fa-times text-red-600';
        case 'recommendation-item':
            return 'fas fa-arrow-right text-blue-600';
        default:
            return 'fas fa-circle text-gray-600';
    }
}

function displayExperienceAnalysis(analysis) {
    const container = document.getElementById('experienceAnalysis');
    const meetsRequirement = analysis.meets_requirement;

    container.innerHTML = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-gray-600">Required:</span>
                <span class="font-medium">${analysis.years_required}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Your Experience:</span>
                <span class="font-medium">${analysis.years_candidate}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Status:</span>
                <span class="font-medium ${meetsRequirement ? 'status-met' : 'status-not-met'}">
                    <i class="fas fa-${meetsRequirement ? 'check' : 'times'} mr-1"></i>
                    ${meetsRequirement ? 'Meets Requirement' : 'Below Requirement'}
                </span>
            </div>
        </div>
    `;
}

function displayEducationAnalysis(analysis) {
    const container = document.getElementById('educationAnalysis');
    const meetsRequirement = analysis.meets_requirement;

    container.innerHTML = `
        <div class="space-y-2">
            <div>
                <span class="text-gray-600 block">Required:</span>
                <span class="font-medium text-sm">${analysis.required}</span>
            </div>
            <div>
                <span class="text-gray-600 block">Your Education:</span>
                <span class="font-medium text-sm">${analysis.candidate}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Status:</span>
                <span class="font-medium ${meetsRequirement ? 'status-met' : 'status-not-met'}">
                    <i class="fas fa-${meetsRequirement ? 'check' : 'times'} mr-1"></i>
                    ${meetsRequirement ? 'Meets Requirement' : 'Below Requirement'}
                </span>
            </div>
        </div>
    `;
}

// UI state management functions
function showLoading() {
    elements.loadingState.classList.remove('hidden');
    elements.resultsSection.classList.add('hidden');
    elements.errorState.classList.add('hidden');
    elements.analyzeBtn.disabled = true;

    // Animate loading steps
    animateLoadingSteps();
}

function hideLoading() {
    elements.loadingState.classList.add('hidden');
    elements.analyzeBtn.disabled = false;
}

function showError(message) {
    elements.errorMessage.textContent = message;
    elements.errorState.classList.remove('hidden');
    elements.resultsSection.classList.add('hidden');
    elements.loadingState.classList.add('hidden');
}

function hideError() {
    elements.errorState.classList.add('hidden');
}

function resetForm() {
    // Clear form
    elements.form.reset();
    removeFile();

    // Reset counters
    updateTextCounter();
    updateJobCounter();

    // Reset status indicators
    updateResumeStatus(false);
    updateJobStatus(false);

    // Reset to first tab
    switchTab('file');

    // Hide all states
    elements.resultsSection.classList.add('hidden');
    elements.loadingState.classList.add('hidden');
    elements.errorState.classList.add('hidden');

    // Reset form validation
    validateFormInputs();

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Animate loading steps
function animateLoadingSteps() {
    const steps = [
        { id: 'loadingStep1', delay: 0 },
        { id: 'loadingStep2', delay: 2000 },
        { id: 'loadingStep3', delay: 4000 }
    ];

    steps.forEach((step, index) => {
        setTimeout(() => {
            const stepElement = document.getElementById(step.id);
            if (stepElement) {
                // Update previous step to completed
                if (index > 0) {
                    const prevStep = document.getElementById(steps[index - 1].id);
                    const spinner = prevStep.querySelector('.animate-spin');
                    const icon = prevStep.querySelector('i');

                    if (spinner) {
                        spinner.classList.add('hidden');
                    }
                    if (icon) {
                        icon.classList.remove('hidden');
                        icon.className = 'fas fa-check text-green-500 mr-2';
                    }
                }

                // Activate current step
                const spinner = stepElement.querySelector('.animate-spin');
                const icon = stepElement.querySelector('i');

                if (spinner) {
                    spinner.classList.remove('hidden');
                }
                if (icon && icon.classList.contains('fa-circle')) {
                    icon.classList.add('hidden');
                }

                stepElement.classList.remove('text-gray-400');
                stepElement.classList.add('text-gray-600');
            }
        }, step.delay);
    });
}

// New UI Functions

// Tab switching functionality
function switchTab(tabType) {
    // Update tab buttons
    document.querySelectorAll('.upload-tab').forEach(tab => {
        tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });

    // Hide all tab contents
    document.querySelectorAll('.upload-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Show selected tab
    switch(tabType) {
        case 'file':
            elements.fileTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            elements.fileTab.classList.remove('border-transparent', 'text-gray-500');
            elements.fileUploadTab.classList.remove('hidden');
            break;
        case 'text':
            elements.textTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            elements.textTab.classList.remove('border-transparent', 'text-gray-500');
            elements.textInputTab.classList.remove('hidden');
            break;
        case 'sample':
            elements.sampleTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            elements.sampleTab.classList.remove('border-transparent', 'text-gray-500');
            elements.sampleResumeTab.classList.remove('hidden');
            break;
    }
}

// Load sample resume
function loadSampleResume() {
    const sampleResume = `John Smith
Software Engineer
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johnsmith
Location: Chicago, IL

PROFESSIONAL SUMMARY
Experienced Software Engineer with 4+ years of expertise in full-stack web development, cloud technologies, and agile methodologies. Proven track record of delivering scalable applications using modern frameworks and AWS services.

TECHNICAL SKILLS
Programming Languages: JavaScript, Python, Java, TypeScript
Frontend: React, Vue.js, HTML5, CSS3, Tailwind CSS
Backend: Node.js, Express.js, Django, Flask
Databases: PostgreSQL, MongoDB, MySQL, Redis
Cloud Platforms: AWS (EC2, S3, Lambda, RDS), Google Cloud Platform
DevOps: Docker, Kubernetes, Jenkins, GitHub Actions

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | Chicago, IL | 2022 - Present
• Developed and maintained 5+ web applications serving 10,000+ daily active users
• Implemented microservices architecture using Node.js and Docker
• Led frontend development using React and TypeScript
• Collaborated with cross-functional teams in agile environment

Software Engineer | StartupXYZ | Remote | 2020 - 2022
• Built responsive web applications using React, Redux, and Material-UI
• Designed and implemented RESTful APIs using Python Flask and PostgreSQL
• Integrated third-party services including payment gateways
• Optimized database queries resulting in 30% improvement in performance

EDUCATION
Bachelor of Science in Computer Science
University of Illinois at Chicago | Chicago, IL | 2015 - 2019
GPA: 3.7/4.0

CERTIFICATIONS
• AWS Certified Developer - Associate (2023)
• Google Analytics Certified (2022)`;

    elements.resumeText.value = sampleResume;
    switchTab('text');
    updateTextCounter();
    updateResumeStatus(true);
    validateFormInputs();

    // Show success message
    showToast('Sample resume loaded successfully!', 'success');
}

// Load sample job description
function loadSampleJob() {
    const sampleJob = `Senior Full Stack Developer - AWS Specialist
TechInnovate Solutions | Chicago, IL | Full-time

About TechInnovate Solutions:
We are a rapidly growing technology company specializing in cloud-native solutions for enterprise clients.

Job Description:
We are seeking a talented Senior Full Stack Developer with strong AWS expertise to join our dynamic engineering team.

Key Responsibilities:
• Design and develop scalable web applications using React, Node.js, and AWS services
• Architect and implement cloud-native solutions using AWS Lambda, API Gateway, S3, and DynamoDB
• Build and maintain RESTful APIs and microservices architecture
• Collaborate with DevOps team to implement CI/CD pipelines
• Optimize application performance and ensure high availability
• Mentor junior developers and participate in code reviews

Required Qualifications:
• Bachelor's degree in Computer Science, Engineering, or related field
• 5+ years of professional software development experience
• Strong proficiency in JavaScript, TypeScript, and modern ES6+ features
• Extensive experience with React.js and state management libraries
• Solid backend development skills with Node.js and Express.js
• Hands-on experience with AWS services including Lambda, API Gateway, S3, DynamoDB
• Experience with containerization using Docker and Kubernetes
• Proficiency with version control systems (Git)
• Strong understanding of database design and optimization
• Experience with automated testing frameworks

Preferred Qualifications:
• AWS certifications (Solutions Architect, Developer, or DevOps Engineer)
• Experience with Infrastructure as Code tools (CloudFormation, CDK)
• Knowledge of machine learning services (Amazon Bedrock, SageMaker)
• Experience with Python for backend development
• Understanding of security best practices

What We Offer:
• Competitive salary range: $120,000 - $160,000
• Comprehensive health, dental, and vision insurance
• 401(k) with company matching
• Flexible work arrangements
• Professional development budget
• Stock options and performance bonuses`;

    elements.jobDescription.value = sampleJob;
    updateJobCounter();
    updateJobStatus(true);
    validateFormInputs();

    // Show success message
    showToast('Sample job description loaded successfully!', 'success');
}

// Clear job description
function clearJobDescription() {
    elements.jobDescription.value = '';
    updateJobCounter();
    updateJobStatus(false);
    validateFormInputs();
}

// Update text counter
function updateTextCounter() {
    const text = elements.resumeText.value;
    const count = text.length;
    elements.textCounter.textContent = `${count.toLocaleString()} characters`;

    // Update resume status
    updateResumeStatus(text.trim().length > 0);
}

// Update job counter
function updateJobCounter() {
    const text = elements.jobDescription.value;
    const count = text.length;
    elements.jobCounter.textContent = `${count.toLocaleString()} characters`;

    // Update job status
    updateJobStatus(text.trim().length > 0);
}

// Update resume status indicator
function updateResumeStatus(hasResume) {
    if (hasResume) {
        elements.resumeStatus.classList.remove('hidden');
        updateProgressStep(1, true);
    } else {
        elements.resumeStatus.classList.add('hidden');
        updateProgressStep(1, false);
    }
}

// Update job status indicator
function updateJobStatus(hasJob) {
    if (hasJob) {
        elements.jobStatus.classList.remove('hidden');
        updateProgressStep(2, true);
    } else {
        elements.jobStatus.classList.add('hidden');
        updateProgressStep(2, false);
    }
}

// Update progress steps
function updateProgressStep(step, completed) {
    const stepElement = elements[`step${step}`];
    const circle = stepElement.querySelector('div');
    const text = stepElement.querySelector('span');

    if (completed) {
        circle.classList.remove('bg-gray-300', 'text-gray-600');
        circle.classList.add('bg-blue-600', 'text-white');
        text.classList.remove('text-gray-500');
        text.classList.add('text-blue-600');
    } else {
        circle.classList.remove('bg-blue-600', 'text-white');
        circle.classList.add('bg-gray-300', 'text-gray-600');
        text.classList.remove('text-blue-600');
        text.classList.add('text-gray-500');
    }
}

// Validate form inputs and enable/disable submit button
function validateFormInputs() {
    const hasFile = currentFile !== null;
    const hasText = elements.resumeText.value.trim() !== '';
    const hasJob = elements.jobDescription.value.trim() !== '';

    const hasResume = hasFile || hasText;
    const canSubmit = hasResume && hasJob;

    elements.analyzeBtn.disabled = !canSubmit;

    if (canSubmit) {
        elements.analyzeBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        elements.analyzeText.textContent = 'Analyze Match';
        updateProgressStep(3, true);
    } else {
        elements.analyzeBtn.classList.add('opacity-50', 'cursor-not-allowed');
        if (!hasResume) {
            elements.analyzeText.textContent = 'Upload Resume First';
        } else if (!hasJob) {
            elements.analyzeText.textContent = 'Add Job Description';
        }
        updateProgressStep(3, false);
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transform transition-all duration-300 translate-x-full`;

    switch(type) {
        case 'success':
            toast.classList.add('bg-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            break;
        default:
            toast.classList.add('bg-blue-500');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Download report functionality
function downloadReport() {
    const resultsData = getCurrentAnalysisResults();
    if (!resultsData) {
        showToast('No analysis results to download', 'error');
        return;
    }

    const reportContent = generateReportContent(resultsData);
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `resume-analysis-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showToast('Report downloaded successfully!', 'success');
}

// Share results functionality
function shareResults() {
    const resultsData = getCurrentAnalysisResults();
    if (!resultsData) {
        showToast('No analysis results to share', 'error');
        return;
    }

    const shareText = `I just analyzed my resume match score and got ${resultsData.match_percentage}%! 🎯\n\nTry the Resume Match Analyzer: ${window.location.href}`;

    if (navigator.share) {
        navigator.share({
            title: 'Resume Match Analysis Results',
            text: shareText,
            url: window.location.href
        }).then(() => {
            showToast('Results shared successfully!', 'success');
        }).catch(() => {
            fallbackShare(shareText);
        });
    } else {
        fallbackShare(shareText);
    }
}

// Fallback share method
function fallbackShare(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Share link copied to clipboard!', 'success');
    }).catch(() => {
        showToast('Unable to share results', 'error');
    });
}

// Get current analysis results
function getCurrentAnalysisResults() {
    // This would store the last analysis results
    return window.lastAnalysisResults || null;
}

// Generate report content
function generateReportContent(results) {
    const date = new Date().toLocaleDateString();

    return `RESUME MATCH ANALYSIS REPORT
Generated on: ${date}

OVERALL MATCH SCORE: ${results.match_percentage}%

ASSESSMENT:
${results.overall_assessment}

STRENGTHS:
${results.strengths.map(item => `• ${item}`).join('\n')}

AREAS FOR IMPROVEMENT:
${results.missing_skills.map(item => `• ${item}`).join('\n')}

RECOMMENDATIONS:
${results.recommendations.map(item => `• ${item}`).join('\n')}

EXPERIENCE ANALYSIS:
• Required: ${results.experience_analysis?.years_required || 'N/A'}
• Your Experience: ${results.experience_analysis?.years_candidate || 'N/A'}
• Status: ${results.experience_analysis?.meets_requirement ? 'Meets Requirement' : 'Below Requirement'}

EDUCATION ANALYSIS:
• Required: ${results.education_analysis?.required || 'N/A'}
• Your Education: ${results.education_analysis?.candidate || 'N/A'}
• Status: ${results.education_analysis?.meets_requirement ? 'Meets Requirement' : 'Below Requirement'}

---
Generated by Resume Match Analyzer
Powered by AWS Lambda & Amazon Bedrock`;
}

// Feedback handling
function handleFeedback(event) {
    const rating = parseInt(event.target.dataset.rating);

    // Update star display
    document.querySelectorAll('.feedback-star').forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('text-gray-300');
            star.classList.add('text-yellow-500');
        } else {
            star.classList.remove('text-yellow-500');
            star.classList.add('text-gray-300');
        }
    });

    // Store feedback (could send to analytics)
    localStorage.setItem('userFeedback', rating);

    showToast(`Thank you for your ${rating}-star feedback!`, 'success');
}

// Highlight stars on hover
function highlightStars(event) {
    const rating = parseInt(event.target.dataset.rating);

    document.querySelectorAll('.feedback-star').forEach((star, index) => {
        if (index < rating) {
            star.classList.add('text-yellow-400');
        }
    });
}

// Reset stars on mouse leave
function resetStars() {
    const currentRating = localStorage.getItem('userFeedback') || 0;

    document.querySelectorAll('.feedback-star').forEach((star, index) => {
        star.classList.remove('text-yellow-400');
        if (index < currentRating) {
            star.classList.add('text-yellow-500');
        } else {
            star.classList.add('text-gray-300');
        }
    });
}

// Enhanced results display to store data
function displayResults(analysis) {
    // Store results for download/share functionality
    window.lastAnalysisResults = analysis;

    hideLoading();
    hideError();

    // Update match percentage with animation
    updateMatchScore(analysis.match_percentage);

    // Update overall assessment
    document.getElementById('overallAssessment').textContent = analysis.overall_assessment;

    // Display strengths
    displayList('strengthsList', analysis.strengths, 'strength-item');

    // Display missing skills
    displayList('missingSkillsList', analysis.missing_skills, 'missing-skill-item');

    // Display recommendations
    displayList('recommendationsList', analysis.recommendations, 'recommendation-item');

    // Display experience analysis
    displayExperienceAnalysis(analysis.experience_analysis);

    // Display education analysis
    displayEducationAnalysis(analysis.education_analysis);

    // Show results section
    elements.resultsSection.classList.remove('hidden');

    // Scroll to results
    elements.resultsSection.scrollIntoView({ behavior: 'smooth' });

    // Show celebration animation for high scores
    if (analysis.match_percentage >= 80) {
        showCelebration();
    }
}

// Show celebration animation
function showCelebration() {
    const celebration = document.createElement('div');
    celebration.innerHTML = '🎉';
    celebration.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-6xl z-50 animate-bounce';
    document.body.appendChild(celebration);

    setTimeout(() => {
        document.body.removeChild(celebration);
    }, 2000);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

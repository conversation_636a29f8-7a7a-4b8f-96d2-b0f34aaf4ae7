// Resume Match Analyzer - Frontend JavaScript

// Configuration
const CONFIG = {
    // AWS API Gateway URL - DEPLOYED!
    API_BASE_URL: 'https://t1l9942pvl.execute-api.us-east-2.amazonaws.com/dev',
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: ['pdf', 'docx', 'txt', 'png', 'jpg', 'jpeg']
};

// Authentication state
let currentUser = null;
let authToken = null;

// Global state
let currentFile = null;

// DOM elements
const elements = {
    form: document.getElementById('analysisForm'),
    dropZone: document.getElementById('dropZone'),
    fileInput: document.getElementById('resumeFile'),
    fileInfo: document.getElementById('fileInfo'),
    fileName: document.getElementById('fileName'),
    fileSize: document.getElementById('fileSize'),
    removeFileBtn: document.getElementById('removeFile'),
    resumeText: document.getElementById('resumeText'),
    jobDescription: document.getElementById('jobDescription'),
    analyzeBtn: document.getElementById('analyzeBtn'),
    analyzeText: document.getElementById('analyzeText'),
    loadingState: document.getElementById('loadingState'),
    resultsSection: document.getElementById('resultsSection'),
    errorState: document.getElementById('errorState'),
    errorMessage: document.getElementById('errorMessage'),
    newAnalysisBtn: document.getElementById('newAnalysisBtn'),

    // New UI elements
    fileTab: document.getElementById('fileTab'),
    textTab: document.getElementById('textTab'),
    sampleTab: document.getElementById('sampleTab'),
    fileUploadTab: document.getElementById('fileUploadTab'),
    textInputTab: document.getElementById('textInputTab'),
    sampleResumeTab: document.getElementById('sampleResumeTab'),
    loadSampleBtn: document.getElementById('loadSampleBtn'),
    loadSampleJobBtn: document.getElementById('loadSampleJobBtn'),
    clearJobBtn: document.getElementById('clearJobBtn'),
    textCounter: document.getElementById('textCounter'),
    jobCounter: document.getElementById('jobCounter'),
    resumeStatus: document.getElementById('resumeStatus'),
    jobStatus: document.getElementById('jobStatus'),

    // Progress steps
    step1: document.getElementById('step1'),
    step2: document.getElementById('step2'),
    step3: document.getElementById('step3')
};

// 🚀 ENHANCED APPLICATION INITIALIZATION 🚀
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    checkApiConnection();
    setupSpectacularEffects();
    setupInteractiveParticles();
    setupHolographicEffects();
    setupThemeSelector();
    initializeAuthentication();

    // Add spectacular loading sequence
    setTimeout(() => {
        document.body.classList.add('loaded');
        triggerWelcomeAnimation();
    }, 100);
});

function initializeEventListeners() {
    // Tab switching
    elements.fileTab.addEventListener('click', () => switchTab('file'));
    elements.textTab.addEventListener('click', () => switchTab('text'));
    elements.sampleTab.addEventListener('click', () => switchTab('sample'));

    // File upload events
    elements.dropZone.addEventListener('click', () => elements.fileInput.click());
    elements.dropZone.addEventListener('dragover', handleDragOver);
    elements.dropZone.addEventListener('dragleave', handleDragLeave);
    elements.dropZone.addEventListener('drop', handleFileDrop);
    elements.fileInput.addEventListener('change', handleFileSelect);
    elements.removeFileBtn.addEventListener('click', removeFile);

    // Sample data loading
    elements.loadSampleBtn.addEventListener('click', loadSampleResume);
    elements.loadSampleJobBtn.addEventListener('click', loadSampleJob);
    elements.clearJobBtn.addEventListener('click', clearJobDescription);

    // Form submission
    elements.form.addEventListener('submit', handleFormSubmit);

    // New analysis button
    elements.newAnalysisBtn.addEventListener('click', resetForm);

    // Results action buttons
    document.getElementById('downloadReportBtn')?.addEventListener('click', downloadReport);
    document.getElementById('shareResultsBtn')?.addEventListener('click', shareResults);

    // Feedback stars
    document.querySelectorAll('.feedback-star').forEach(star => {
        star.addEventListener('click', handleFeedback);
        star.addEventListener('mouseenter', highlightStars);
        star.addEventListener('mouseleave', resetStars);
    });

    // Text counters
    elements.resumeText.addEventListener('input', updateTextCounter);
    elements.jobDescription.addEventListener('input', updateJobCounter);

    // Form validation
    elements.resumeText.addEventListener('input', validateFormInputs);
    elements.jobDescription.addEventListener('input', validateFormInputs);
    elements.fileInput.addEventListener('change', validateFormInputs);

    // Clear resume text when file is uploaded and vice versa
    elements.fileInput.addEventListener('change', () => {
        if (elements.fileInput.files.length > 0) {
            elements.resumeText.value = '';
            updateTextCounter();
        }
    });

    elements.resumeText.addEventListener('input', () => {
        if (elements.resumeText.value.trim()) {
            removeFile();
        }
    });
}

async function checkApiConnection() {
    try {
        const response = await axios.get(`${CONFIG.API_BASE_URL}/health`);
        console.log('API connection successful:', response.data);
    } catch (error) {
        console.warn('API connection failed. Using demo mode.');
        // In demo mode, we'll simulate responses
    }
}

// File handling functions
function handleDragOver(e) {
    e.preventDefault();
    elements.dropZone.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    elements.dropZone.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    elements.dropZone.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFile(file) {
    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
        showError(validation.message);
        return;
    }

    currentFile = file;
    showFileInfo(file);
    hideError();
}

function validateFile(file) {
    // Check file size
    if (file.size > CONFIG.MAX_FILE_SIZE) {
        return {
            valid: false,
            message: 'File size must be less than 5MB'
        };
    }

    // Check file type
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!CONFIG.ALLOWED_FILE_TYPES.includes(fileExtension)) {
        return {
            valid: false,
            message: 'Only PDF, DOCX, and TXT files are supported'
        };
    }

    return { valid: true };
}

function showFileInfo(file) {
    elements.fileName.textContent = file.name;
    elements.fileSize.textContent = formatFileSize(file.size);
    elements.fileInfo.classList.remove('hidden');
    updateResumeStatus(true);
}

function removeFile() {
    currentFile = null;
    elements.fileInput.value = '';
    elements.fileInfo.classList.add('hidden');
    updateResumeStatus(false);
    validateFormInputs();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission and analysis
async function handleFormSubmit(e) {
    e.preventDefault();
    
    // Validate form
    const validation = validateForm();
    if (!validation.valid) {
        showError(validation.message);
        return;
    }

    try {
        showLoading();
        hideError();
        
        const analysisData = await prepareAnalysisData();
        const result = await performAnalysis(analysisData);
        
        displayResults(result);
        
    } catch (error) {
        console.error('Analysis failed:', error);

        // Check if it's a validation error
        if (error.message && error.message.includes('Document validation failed')) {
            showValidationError(error);
        } else {
            showError(error.message || 'Analysis failed. Please try again.');
        }
    } finally {
        hideLoading();
    }
}

function validateForm() {
    const hasFile = currentFile !== null;
    const hasText = elements.resumeText.value.trim() !== '';
    const hasJobDescription = elements.jobDescription.value.trim() !== '';

    if (!hasFile && !hasText) {
        return {
            valid: false,
            message: 'Please upload a resume file or paste resume text'
        };
    }

    if (!hasJobDescription) {
        return {
            valid: false,
            message: 'Please provide a job description'
        };
    }

    return { valid: true };
}

async function prepareAnalysisData() {
    const data = {
        job_description: elements.jobDescription.value.trim()
    };

    if (currentFile) {
        // Convert file to base64
        const fileBase64 = await fileToBase64(currentFile);
        data.resume_file = fileBase64;
        data.file_type = currentFile.name.split('.').pop().toLowerCase();
    } else {
        data.resume_text = elements.resumeText.value.trim();
    }

    return data;
}

function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove the data:application/pdf;base64, prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = error => reject(error);
    });
}

async function performAnalysis(data) {
    try {
        const response = await axios.post(`${CONFIG.API_BASE_URL}/analyze`, data, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 second timeout
        });

        if (response.data.success) {
            return response.data.analysis;
        } else {
            throw new Error(response.data.error || 'Analysis failed');
        }
    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            throw new Error('Analysis timed out. Please try again.');
        } else if (error.response) {
            // Check for validation errors
            if (error.response.data && error.response.data.validation) {
                const validation = error.response.data.validation;
                const validationError = new Error('Document validation failed');
                validationError.validation = validation;
                throw validationError;
            }
            throw new Error(error.response.data.error || 'Server error occurred');
        } else {
            // Fallback to demo mode for development
            console.warn('Using demo mode');
            return getDemoAnalysis();
        }
    }
}

function getDemoAnalysis() {
    // Enhanced demo data with Gemini integration
    return {
        match_percentage: 78,
        overall_assessment: "Your resume shows a strong match for this position with relevant experience and skills. There are several areas where you could strengthen your profile to become an even better candidate. The analysis was powered by Google Gemini AI.",
        strengths: [
            "Strong technical background in required programming languages",
            "Relevant work experience in similar industry",
            "Good educational background matching job requirements",
            "Demonstrated leadership and project management skills"
        ],
        missing_skills: [
            "Cloud computing experience (AWS/Azure/GCP)",
            "Advanced data analysis and machine learning skills",
            "DevOps and CI/CD pipeline experience"
        ],
        recommendations: [
            "Consider obtaining cloud certifications (AWS, Azure, or GCP)",
            "Highlight any project management experience more prominently",
            "Add specific examples of data analysis or ML projects",
            "Include metrics and quantifiable achievements in your experience"
        ],
        keyword_matches: {
            matched: ["JavaScript", "Python", "React", "Node.js", "SQL", "Git"],
            missing: ["AWS", "Docker", "Kubernetes", "CI/CD", "Machine Learning"]
        },
        experience_analysis: {
            years_required: "3-5 years",
            years_candidate: "4 years",
            meets_requirement: true
        },
        education_analysis: {
            required: "Bachelor's degree in Computer Science or related field",
            candidate: "Bachelor's in Computer Science",
            meets_requirement: true
        }
    };
}

// Results display functions (moved to enhanced version below)

function updateMatchScore(percentage) {
    const circle = document.getElementById('matchScoreCircle');
    const percentageElement = document.getElementById('matchPercentage');

    // Animate the percentage text
    animateNumber(percentageElement, 0, percentage, 1500);

    // Animate the circle
    const circumference = 2 * Math.PI * 15.9155;
    const offset = circumference - (percentage / 100) * circumference;

    setTimeout(() => {
        circle.style.strokeDasharray = `${circumference}, ${circumference}`;
        circle.style.strokeDashoffset = offset;
    }, 100);

    // Update circle color based on score
    if (percentage >= 80) {
        circle.classList.remove('text-blue-600', 'text-yellow-500');
        circle.classList.add('text-green-600');
    } else if (percentage >= 60) {
        circle.classList.remove('text-blue-600', 'text-green-600');
        circle.classList.add('text-yellow-500');
    } else {
        circle.classList.remove('text-green-600', 'text-yellow-500');
        circle.classList.add('text-red-600');
    }
}

function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.round(start + (end - start) * progress);
        element.textContent = `${current}%`;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

function displayList(containerId, items, itemClass) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';

    items.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = `${itemClass} list-item p-3 rounded-lg`;
        li.style.animationDelay = `${index * 0.1}s`;

        const icon = getIconForItemType(itemClass);
        li.innerHTML = `
            <div class="flex items-start">
                <i class="${icon} mt-1 mr-3 flex-shrink-0"></i>
                <span class="text-gray-800">${item}</span>
            </div>
        `;

        container.appendChild(li);
    });
}

function getIconForItemType(itemClass) {
    switch (itemClass) {
        case 'strength-item':
            return 'fas fa-check text-green-600';
        case 'missing-skill-item':
            return 'fas fa-times text-red-600';
        case 'recommendation-item':
            return 'fas fa-arrow-right text-blue-600';
        default:
            return 'fas fa-circle text-gray-600';
    }
}

function displayExperienceAnalysis(analysis) {
    const container = document.getElementById('experienceAnalysis');
    const meetsRequirement = analysis.meets_requirement;

    container.innerHTML = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-gray-600">Required:</span>
                <span class="font-medium">${analysis.years_required}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Your Experience:</span>
                <span class="font-medium">${analysis.years_candidate}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Status:</span>
                <span class="font-medium ${meetsRequirement ? 'status-met' : 'status-not-met'}">
                    <i class="fas fa-${meetsRequirement ? 'check' : 'times'} mr-1"></i>
                    ${meetsRequirement ? 'Meets Requirement' : 'Below Requirement'}
                </span>
            </div>
        </div>
    `;
}

function displayEducationAnalysis(analysis) {
    const container = document.getElementById('educationAnalysis');
    const meetsRequirement = analysis.meets_requirement;

    container.innerHTML = `
        <div class="space-y-2">
            <div>
                <span class="text-gray-600 block">Required:</span>
                <span class="font-medium text-sm">${analysis.required}</span>
            </div>
            <div>
                <span class="text-gray-600 block">Your Education:</span>
                <span class="font-medium text-sm">${analysis.candidate}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Status:</span>
                <span class="font-medium ${meetsRequirement ? 'status-met' : 'status-not-met'}">
                    <i class="fas fa-${meetsRequirement ? 'check' : 'times'} mr-1"></i>
                    ${meetsRequirement ? 'Meets Requirement' : 'Below Requirement'}
                </span>
            </div>
        </div>
    `;
}

// UI state management functions
function showLoading() {
    elements.loadingState.classList.remove('hidden');
    elements.resultsSection.classList.add('hidden');
    elements.errorState.classList.add('hidden');
    elements.analyzeBtn.disabled = true;

    // Animate loading steps
    animateLoadingSteps();
}

function hideLoading() {
    elements.loadingState.classList.add('hidden');
    elements.analyzeBtn.disabled = false;
}

function showError(message) {
    elements.errorMessage.textContent = message;
    elements.errorState.classList.remove('hidden');
    elements.resultsSection.classList.add('hidden');
    elements.loadingState.classList.add('hidden');
}

function hideError() {
    elements.errorState.classList.add('hidden');
}

function resetForm() {
    // Clear form
    elements.form.reset();
    removeFile();

    // Reset counters
    updateTextCounter();
    updateJobCounter();

    // Reset status indicators
    updateResumeStatus(false);
    updateJobStatus(false);

    // Reset to first tab
    switchTab('file');

    // Hide all states
    elements.resultsSection.classList.add('hidden');
    elements.loadingState.classList.add('hidden');
    elements.errorState.classList.add('hidden');

    // Reset form validation
    validateFormInputs();

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Enhanced loading animation with progress and tips
function animateLoadingSteps() {
    const steps = [
        { id: 'loadingStep1', delay: 0, progress: 33 },
        { id: 'loadingStep2', delay: 3000, progress: 66 },
        { id: 'loadingStep3', delay: 6000, progress: 100 }
    ];

    const tips = [
        "Our AI analyzes over 50 different factors in your resume!",
        "We compare your skills against industry standards and job requirements.",
        "Machine learning helps us identify the most relevant improvements.",
        "Our analysis considers both hard and soft skills mentioned in the job posting.",
        "We use natural language processing to understand context and nuance."
    ];

    let tipIndex = 0;

    // Rotate tips every 2 seconds
    const tipInterval = setInterval(() => {
        const tipElement = document.getElementById('loadingTip');
        if (tipElement) {
            tipElement.style.opacity = '0';
            setTimeout(() => {
                tipElement.textContent = tips[tipIndex % tips.length];
                tipElement.style.opacity = '1';
                tipIndex++;
            }, 300);
        }
    }, 2000);

    steps.forEach((step, index) => {
        setTimeout(() => {
            // Update progress bar
            const progressBar = document.getElementById('loadingProgressBar');
            const progressText = document.getElementById('loadingProgress');
            if (progressBar && progressText) {
                progressBar.style.width = `${step.progress}%`;
                progressText.textContent = `${step.progress}%`;
            }

            const stepElement = document.getElementById(step.id);
            if (stepElement) {
                // Complete previous step
                if (index > 0) {
                    const prevStep = document.getElementById(steps[index - 1].id);
                    if (prevStep) {
                        prevStep.classList.remove('opacity-50');
                        const prevIcon = prevStep.querySelector('.w-8 > div, .w-8 > i');
                        const prevStatus = prevStep.querySelector('div:last-child');

                        if (prevIcon) {
                            prevIcon.className = '';
                            prevIcon.innerHTML = '<i class="fas fa-check text-white text-sm"></i>';
                        }
                        if (prevStatus) {
                            prevStatus.textContent = '✓ Complete';
                            prevStatus.className = 'text-green-400 text-sm';
                        }
                    }
                }

                // Activate current step
                stepElement.classList.remove('opacity-50');
                const currentStatus = stepElement.querySelector('div:last-child');
                if (currentStatus && index < steps.length - 1) {
                    currentStatus.textContent = 'Processing...';
                    currentStatus.className = 'text-blue-400 text-sm animate-pulse';
                }
            }
        }, step.delay);
    });

    // Complete final step
    setTimeout(() => {
        const finalStep = document.getElementById('loadingStep3');
        if (finalStep) {
            const finalIcon = finalStep.querySelector('.w-8 > div, .w-8 > i');
            const finalStatus = finalStep.querySelector('div:last-child');

            if (finalIcon) {
                finalIcon.className = 'w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4';
                finalIcon.innerHTML = '<i class="fas fa-check text-white text-sm"></i>';
            }
            if (finalStatus) {
                finalStatus.textContent = '✓ Complete';
                finalStatus.className = 'text-green-400 text-sm';
            }
        }

        // Clear tip interval
        clearInterval(tipInterval);
    }, 9000);
}

// New UI Functions

// Tab switching functionality
function switchTab(tabType) {
    // Update tab buttons
    document.querySelectorAll('.upload-tab').forEach(tab => {
        tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });

    // Hide all tab contents
    document.querySelectorAll('.upload-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Show selected tab
    switch(tabType) {
        case 'file':
            elements.fileTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            elements.fileTab.classList.remove('border-transparent', 'text-gray-500');
            elements.fileUploadTab.classList.remove('hidden');
            break;
        case 'text':
            elements.textTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            elements.textTab.classList.remove('border-transparent', 'text-gray-500');
            elements.textInputTab.classList.remove('hidden');
            break;
        case 'sample':
            elements.sampleTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            elements.sampleTab.classList.remove('border-transparent', 'text-gray-500');
            elements.sampleResumeTab.classList.remove('hidden');
            break;
    }
}

// Load sample resume
function loadSampleResume() {
    const sampleResume = `John Smith
Software Engineer
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johnsmith
Location: Chicago, IL

PROFESSIONAL SUMMARY
Experienced Software Engineer with 4+ years of expertise in full-stack web development, cloud technologies, and agile methodologies. Proven track record of delivering scalable applications using modern frameworks and AWS services.

TECHNICAL SKILLS
Programming Languages: JavaScript, Python, Java, TypeScript
Frontend: React, Vue.js, HTML5, CSS3, Tailwind CSS
Backend: Node.js, Express.js, Django, Flask
Databases: PostgreSQL, MongoDB, MySQL, Redis
Cloud Platforms: AWS (EC2, S3, Lambda, RDS), Google Cloud Platform
DevOps: Docker, Kubernetes, Jenkins, GitHub Actions

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | Chicago, IL | 2022 - Present
• Developed and maintained 5+ web applications serving 10,000+ daily active users
• Implemented microservices architecture using Node.js and Docker
• Led frontend development using React and TypeScript
• Collaborated with cross-functional teams in agile environment

Software Engineer | StartupXYZ | Remote | 2020 - 2022
• Built responsive web applications using React, Redux, and Material-UI
• Designed and implemented RESTful APIs using Python Flask and PostgreSQL
• Integrated third-party services including payment gateways
• Optimized database queries resulting in 30% improvement in performance

EDUCATION
Bachelor of Science in Computer Science
University of Illinois at Chicago | Chicago, IL | 2015 - 2019
GPA: 3.7/4.0

CERTIFICATIONS
• AWS Certified Developer - Associate (2023)
• Google Analytics Certified (2022)`;

    elements.resumeText.value = sampleResume;
    switchTab('text');
    updateTextCounter();
    updateResumeStatus(true);
    validateFormInputs();

    // Show success message
    showToast('Sample resume loaded successfully!', 'success');
}

// Load sample job description
function loadSampleJob() {
    const sampleJob = `Senior Full Stack Developer - AWS Specialist
TechInnovate Solutions | Chicago, IL | Full-time

About TechInnovate Solutions:
We are a rapidly growing technology company specializing in cloud-native solutions for enterprise clients.

Job Description:
We are seeking a talented Senior Full Stack Developer with strong AWS expertise to join our dynamic engineering team.

Key Responsibilities:
• Design and develop scalable web applications using React, Node.js, and AWS services
• Architect and implement cloud-native solutions using AWS Lambda, API Gateway, S3, and DynamoDB
• Build and maintain RESTful APIs and microservices architecture
• Collaborate with DevOps team to implement CI/CD pipelines
• Optimize application performance and ensure high availability
• Mentor junior developers and participate in code reviews

Required Qualifications:
• Bachelor's degree in Computer Science, Engineering, or related field
• 5+ years of professional software development experience
• Strong proficiency in JavaScript, TypeScript, and modern ES6+ features
• Extensive experience with React.js and state management libraries
• Solid backend development skills with Node.js and Express.js
• Hands-on experience with AWS services including Lambda, API Gateway, S3, DynamoDB
• Experience with containerization using Docker and Kubernetes
• Proficiency with version control systems (Git)
• Strong understanding of database design and optimization
• Experience with automated testing frameworks

Preferred Qualifications:
• AWS certifications (Solutions Architect, Developer, or DevOps Engineer)
• Experience with Infrastructure as Code tools (CloudFormation, CDK)
• Knowledge of machine learning services (Amazon Bedrock, SageMaker)
• Experience with Python for backend development
• Understanding of security best practices

What We Offer:
• Competitive salary range: $120,000 - $160,000
• Comprehensive health, dental, and vision insurance
• 401(k) with company matching
• Flexible work arrangements
• Professional development budget
• Stock options and performance bonuses`;

    elements.jobDescription.value = sampleJob;
    updateJobCounter();
    updateJobStatus(true);
    validateFormInputs();

    // Show success message
    showToast('Sample job description loaded successfully!', 'success');
}

// Clear job description
function clearJobDescription() {
    elements.jobDescription.value = '';
    updateJobCounter();
    updateJobStatus(false);
    validateFormInputs();
}

// Update text counter
function updateTextCounter() {
    const text = elements.resumeText.value;
    const count = text.length;
    elements.textCounter.textContent = `${count.toLocaleString()} characters`;

    // Update resume status
    updateResumeStatus(text.trim().length > 0);
}

// Update job counter
function updateJobCounter() {
    const text = elements.jobDescription.value;
    const count = text.length;
    elements.jobCounter.textContent = `${count.toLocaleString()} characters`;

    // Update job status
    updateJobStatus(text.trim().length > 0);
}

// Update resume status indicator
function updateResumeStatus(hasResume) {
    if (hasResume) {
        elements.resumeStatus.classList.remove('hidden');
        updateProgressStep(1, true);
    } else {
        elements.resumeStatus.classList.add('hidden');
        updateProgressStep(1, false);
    }
}

// Update job status indicator
function updateJobStatus(hasJob) {
    if (hasJob) {
        elements.jobStatus.classList.remove('hidden');
        updateProgressStep(2, true);
    } else {
        elements.jobStatus.classList.add('hidden');
        updateProgressStep(2, false);
    }
}

// Update progress steps
function updateProgressStep(step, completed) {
    const stepElement = elements[`step${step}`];
    const circle = stepElement.querySelector('div');
    const text = stepElement.querySelector('span');

    if (completed) {
        circle.classList.remove('bg-gray-300', 'text-gray-600');
        circle.classList.add('bg-blue-600', 'text-white');
        text.classList.remove('text-gray-500');
        text.classList.add('text-blue-600');
    } else {
        circle.classList.remove('bg-blue-600', 'text-white');
        circle.classList.add('bg-gray-300', 'text-gray-600');
        text.classList.remove('text-blue-600');
        text.classList.add('text-gray-500');
    }
}

// Validate form inputs and enable/disable submit button
function validateFormInputs() {
    const hasFile = currentFile !== null;
    const hasText = elements.resumeText.value.trim() !== '';
    const hasJob = elements.jobDescription.value.trim() !== '';

    const hasResume = hasFile || hasText;
    const canSubmit = hasResume && hasJob;

    elements.analyzeBtn.disabled = !canSubmit;

    if (canSubmit) {
        elements.analyzeBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        elements.analyzeText.textContent = 'Analyze Match';
        updateProgressStep(3, true);
    } else {
        elements.analyzeBtn.classList.add('opacity-50', 'cursor-not-allowed');
        if (!hasResume) {
            elements.analyzeText.textContent = 'Upload Resume First';
        } else if (!hasJob) {
            elements.analyzeText.textContent = 'Add Job Description';
        }
        updateProgressStep(3, false);
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transform transition-all duration-300 translate-x-full`;

    switch(type) {
        case 'success':
            toast.classList.add('bg-green-500');
            break;
        case 'error':
            toast.classList.add('bg-red-500');
            break;
        default:
            toast.classList.add('bg-blue-500');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Download report functionality
function downloadReport() {
    const resultsData = getCurrentAnalysisResults();
    if (!resultsData) {
        showToast('No analysis results to download', 'error');
        return;
    }

    const reportContent = generateReportContent(resultsData);
    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `resume-analysis-report-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showToast('Report downloaded successfully!', 'success');
}

// Share results functionality
function shareResults() {
    const resultsData = getCurrentAnalysisResults();
    if (!resultsData) {
        showToast('No analysis results to share', 'error');
        return;
    }

    const shareText = `I just analyzed my resume match score and got ${resultsData.match_percentage}%! 🎯\n\nTry the Resume Match Analyzer: ${window.location.href}`;

    if (navigator.share) {
        navigator.share({
            title: 'Resume Match Analysis Results',
            text: shareText,
            url: window.location.href
        }).then(() => {
            showToast('Results shared successfully!', 'success');
        }).catch(() => {
            fallbackShare(shareText);
        });
    } else {
        fallbackShare(shareText);
    }
}

// Fallback share method
function fallbackShare(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Share link copied to clipboard!', 'success');
    }).catch(() => {
        showToast('Unable to share results', 'error');
    });
}

// Get current analysis results
function getCurrentAnalysisResults() {
    // This would store the last analysis results
    return window.lastAnalysisResults || null;
}

// Generate report content
function generateReportContent(results) {
    const date = new Date().toLocaleDateString();

    return `RESUME MATCH ANALYSIS REPORT
Generated on: ${date}

OVERALL MATCH SCORE: ${results.match_percentage}%

ASSESSMENT:
${results.overall_assessment}

STRENGTHS:
${results.strengths.map(item => `• ${item}`).join('\n')}

AREAS FOR IMPROVEMENT:
${results.missing_skills.map(item => `• ${item}`).join('\n')}

RECOMMENDATIONS:
${results.recommendations.map(item => `• ${item}`).join('\n')}

EXPERIENCE ANALYSIS:
• Required: ${results.experience_analysis?.years_required || 'N/A'}
• Your Experience: ${results.experience_analysis?.years_candidate || 'N/A'}
• Status: ${results.experience_analysis?.meets_requirement ? 'Meets Requirement' : 'Below Requirement'}

EDUCATION ANALYSIS:
• Required: ${results.education_analysis?.required || 'N/A'}
• Your Education: ${results.education_analysis?.candidate || 'N/A'}
• Status: ${results.education_analysis?.meets_requirement ? 'Meets Requirement' : 'Below Requirement'}

---
Generated by Resume Match Analyzer
Powered by AWS Lambda & Amazon Bedrock`;
}

// Feedback handling
function handleFeedback(event) {
    const rating = parseInt(event.target.dataset.rating);

    // Update star display
    document.querySelectorAll('.feedback-star').forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('text-gray-300');
            star.classList.add('text-yellow-500');
        } else {
            star.classList.remove('text-yellow-500');
            star.classList.add('text-gray-300');
        }
    });

    // Store feedback (could send to analytics)
    localStorage.setItem('userFeedback', rating);

    showToast(`Thank you for your ${rating}-star feedback!`, 'success');
}

// Highlight stars on hover
function highlightStars(event) {
    const rating = parseInt(event.target.dataset.rating);

    document.querySelectorAll('.feedback-star').forEach((star, index) => {
        if (index < rating) {
            star.classList.add('text-yellow-400');
        }
    });
}

// Reset stars on mouse leave
function resetStars() {
    const currentRating = localStorage.getItem('userFeedback') || 0;

    document.querySelectorAll('.feedback-star').forEach((star, index) => {
        star.classList.remove('text-yellow-400');
        if (index < currentRating) {
            star.classList.add('text-yellow-500');
        } else {
            star.classList.add('text-gray-300');
        }
    });
}

// Enhanced results display with AWS analysis
function displayResults(analysis) {
    // Store results for download/share functionality
    window.lastAnalysisResults = analysis;

    hideLoading();
    hideError();

    // Update match percentage with enhanced animation
    updateMatchScore(analysis.match_percentage);

    // Update score interpretation
    updateScoreInterpretation(analysis.match_percentage);

    // Update overall assessment
    document.getElementById('overallAssessment').textContent = analysis.overall_assessment;

    // Display AWS analysis insights if available
    if (analysis.aws_document_analysis) {
        displayAWSAnalysisInsights(analysis.aws_document_analysis);
    }

    // Display strengths with metrics
    displayEnhancedList('strengthsList', analysis.strengths, 'strength-item');
    updateStrengthMeter(analysis.strengths.length);

    // Display missing skills with metrics
    displayEnhancedList('missingSkillsList', analysis.missing_skills, 'missing-skill-item');
    updateImprovementMeter(analysis.missing_skills.length);

    // Display recommendations
    displayEnhancedList('recommendationsList', analysis.recommendations, 'recommendation-item');

    // Display experience analysis
    displayExperienceAnalysis(analysis.experience_analysis);

    // Display education analysis
    displayEducationAnalysis(analysis.education_analysis);

    // Show results section with staggered animation
    elements.resultsSection.classList.remove('hidden');

    // Animate sections in sequence
    animateResultsSections();

    // Scroll to results
    setTimeout(() => {
        elements.resultsSection.scrollIntoView({ behavior: 'smooth' });
    }, 500);

    // Show celebration animation for high scores
    if (analysis.match_percentage >= 80) {
        setTimeout(() => showCelebration(), 1000);
    }
}

// Update score interpretation with visual feedback
function updateScoreInterpretation(percentage) {
    const interpretation = document.getElementById('scoreInterpretation');
    const icon = document.getElementById('scoreIcon');
    const text = document.getElementById('scoreText');

    if (!interpretation || !icon || !text) return;

    let config;

    if (percentage >= 90) {
        config = {
            bg: 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-400/30',
            icon: 'fas fa-star text-yellow-400',
            text: 'Exceptional Match!',
            color: 'text-green-200'
        };
    } else if (percentage >= 80) {
        config = {
            bg: 'bg-gradient-to-r from-blue-500/20 to-green-500/20 border-blue-400/30',
            icon: 'fas fa-thumbs-up text-green-400',
            text: 'Excellent Match',
            color: 'text-blue-200'
        };
    } else if (percentage >= 70) {
        config = {
            bg: 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-400/30',
            icon: 'fas fa-check text-blue-400',
            text: 'Good Match',
            color: 'text-blue-200'
        };
    } else if (percentage >= 60) {
        config = {
            bg: 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-400/30',
            icon: 'fas fa-exclamation text-yellow-400',
            text: 'Fair Match',
            color: 'text-yellow-200'
        };
    } else {
        config = {
            bg: 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-400/30',
            icon: 'fas fa-times text-red-400',
            text: 'Needs Improvement',
            color: 'text-orange-200'
        };
    }

    interpretation.className = `inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${config.bg} ${config.color}`;
    icon.className = config.icon;
    text.textContent = config.text;
}

// Enhanced list display with better animations
function displayEnhancedList(containerId, items, itemClass) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = '';

    items.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = `${itemClass} list-item p-4 rounded-xl transition-all duration-300 transform hover:scale-105`;
        li.style.opacity = '0';
        li.style.transform = 'translateY(20px)';
        li.style.animationDelay = `${index * 0.1}s`;

        const icon = getEnhancedIconForItemType(itemClass);
        li.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0 mr-4">
                    ${icon}
                </div>
                <div class="flex-1">
                    <span class="text-white font-medium">${item}</span>
                </div>
            </div>
        `;

        container.appendChild(li);

        // Animate in
        setTimeout(() => {
            li.style.opacity = '1';
            li.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Enhanced icons for list items
function getEnhancedIconForItemType(itemClass) {
    switch (itemClass) {
        case 'strength-item':
            return '<div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center"><i class="fas fa-check text-white text-sm"></i></div>';
        case 'missing-skill-item':
            return '<div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center"><i class="fas fa-plus text-white text-sm"></i></div>';
        case 'recommendation-item':
            return '<div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center"><i class="fas fa-lightbulb text-white text-sm"></i></div>';
        default:
            return '<div class="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-lg flex items-center justify-center"><i class="fas fa-circle text-white text-sm"></i></div>';
    }
}

// Update strength meter
function updateStrengthMeter(strengthCount) {
    const percentage = Math.min((strengthCount / 5) * 100, 100); // Assume max 5 strengths for 100%
    const bar = document.getElementById('strengthBar');
    const text = document.getElementById('strengthPercentage');

    if (bar && text) {
        setTimeout(() => {
            bar.style.width = `${percentage}%`;
            text.textContent = `${Math.round(percentage)}%`;
        }, 500);
    }
}

// Update improvement meter
function updateImprovementMeter(improvementCount) {
    const percentage = Math.min((improvementCount / 3) * 100, 100); // Assume max 3 improvements for 100%
    const bar = document.getElementById('improvementBar');
    const text = document.getElementById('improvementPercentage');

    if (bar && text) {
        setTimeout(() => {
            bar.style.width = `${percentage}%`;
            text.textContent = `${Math.round(percentage)}%`;
        }, 700);
    }
}

// Display AWS analysis insights
function displayAWSAnalysisInsights(awsAnalysis) {
    // Create AWS insights section if it doesn't exist
    let awsSection = document.getElementById('awsInsightsSection');
    if (!awsSection) {
        awsSection = createAWSInsightsSection();
        // Insert after the main score card
        const scoreCard = elements.resultsSection.querySelector('.relative.group');
        scoreCard.parentNode.insertBefore(awsSection, scoreCard.nextSibling);
    }

    // Update AWS confidence score
    const awsConfidence = document.getElementById('awsConfidence');
    if (awsConfidence) {
        awsConfidence.textContent = `${Math.round(awsAnalysis.confidence * 100)}%`;
    }

    // Display Textract confidence if available
    const textractConfidence = awsAnalysis.textract_confidence;
    if (textractConfidence) {
        updateTextractMetrics(textractConfidence);
    }

    // Display Comprehend entities
    const entities = awsAnalysis.comprehend_entities || [];
    displayComprehendEntities(entities);

    // Display key phrases
    const keyPhrases = awsAnalysis.comprehend_key_phrases || [];
    displayKeyPhrases(keyPhrases);

    // Display structured data insights
    const structuredData = awsAnalysis.structured_data || {};
    displayStructuredDataInsights(structuredData);
}

// Create AWS insights section
function createAWSInsightsSection() {
    const section = document.createElement('div');
    section.id = 'awsInsightsSection';
    section.className = 'relative group mb-8';

    section.innerHTML = `
        <div class="absolute -inset-1 bg-gradient-to-r from-orange-600 to-yellow-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>

        <div class="relative bg-white/10 backdrop-blur-2xl border border-white/20 rounded-2xl p-6 shadow-2xl">
            <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                    <i class="fab fa-aws text-white text-xl"></i>
                </div>
                <div>
                    <h4 class="text-xl font-bold text-white">AWS Document Analysis</h4>
                    <p class="text-orange-200 text-sm">Powered by Amazon Textract & Comprehend</p>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-2xl font-bold text-orange-300" id="awsConfidence">--</div>
                    <div class="text-xs text-orange-200">AWS Confidence</div>
                </div>
            </div>

            <!-- AWS Analysis Details -->
            <div class="grid md:grid-cols-2 gap-6">
                <!-- Textract Results -->
                <div class="bg-white/5 rounded-xl p-4">
                    <h5 class="text-white font-semibold mb-3 flex items-center">
                        <i class="fas fa-file-text text-orange-400 mr-2"></i>
                        Document Structure
                    </h5>
                    <div id="textractMetrics" class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-orange-200">Text Extraction:</span>
                            <span class="text-white" id="textExtraction">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-orange-200">Layout Analysis:</span>
                            <span class="text-white" id="layoutAnalysis">--</span>
                        </div>
                    </div>
                </div>

                <!-- Comprehend Results -->
                <div class="bg-white/5 rounded-xl p-4">
                    <h5 class="text-white font-semibold mb-3 flex items-center">
                        <i class="fas fa-brain text-orange-400 mr-2"></i>
                        Content Analysis
                    </h5>
                    <div id="comprehendMetrics" class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-orange-200">Entities Found:</span>
                            <span class="text-white" id="entitiesCount">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-orange-200">Key Phrases:</span>
                            <span class="text-white" id="keyPhrasesCount">--</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Entities and Key Phrases -->
            <div class="mt-6 grid md:grid-cols-2 gap-6">
                <div>
                    <h6 class="text-white font-medium mb-3">Detected Entities</h6>
                    <div id="entitiesList" class="space-y-1 max-h-32 overflow-y-auto"></div>
                </div>
                <div>
                    <h6 class="text-white font-medium mb-3">Key Phrases</h6>
                    <div id="keyPhrasesList" class="space-y-1 max-h-32 overflow-y-auto"></div>
                </div>
            </div>
        </div>
    `;

    return section;
}

// Update Textract metrics
function updateTextractMetrics(confidence) {
    const textExtraction = document.getElementById('textExtraction');
    const layoutAnalysis = document.getElementById('layoutAnalysis');

    if (textExtraction && confidence.text_extraction) {
        textExtraction.textContent = `${Math.round(confidence.text_extraction)}%`;
    }

    if (layoutAnalysis && confidence.layout_analysis) {
        layoutAnalysis.textContent = `${Math.round(confidence.layout_analysis)}%`;
    }
}

// Display Comprehend entities
function displayComprehendEntities(entities) {
    const entitiesList = document.getElementById('entitiesList');
    const entitiesCount = document.getElementById('entitiesCount');

    if (!entitiesList || !entitiesCount) return;

    entitiesCount.textContent = entities.length;
    entitiesList.innerHTML = '';

    // Group entities by type
    const entityTypes = {};
    entities.forEach(entity => {
        if (!entityTypes[entity.Type]) {
            entityTypes[entity.Type] = [];
        }
        entityTypes[entity.Type].push(entity);
    });

    // Display top entities
    Object.entries(entityTypes).slice(0, 5).forEach(([type, typeEntities]) => {
        const entityDiv = document.createElement('div');
        entityDiv.className = 'flex justify-between text-xs';
        entityDiv.innerHTML = `
            <span class="text-orange-200">${type}:</span>
            <span class="text-white">${typeEntities.length}</span>
        `;
        entitiesList.appendChild(entityDiv);
    });
}

// Display key phrases
function displayKeyPhrases(keyPhrases) {
    const keyPhrasesList = document.getElementById('keyPhrasesList');
    const keyPhrasesCount = document.getElementById('keyPhrasesCount');

    if (!keyPhrasesList || !keyPhrasesCount) return;

    keyPhrasesCount.textContent = keyPhrases.length;
    keyPhrasesList.innerHTML = '';

    // Display top key phrases
    keyPhrases.slice(0, 5).forEach(phrase => {
        const phraseDiv = document.createElement('div');
        phraseDiv.className = 'text-xs text-orange-100 bg-white/5 rounded px-2 py-1';
        phraseDiv.textContent = phrase.Text;
        keyPhrasesList.appendChild(phraseDiv);
    });
}

// Display structured data insights
function displayStructuredDataInsights(structuredData) {
    // This could be expanded to show contact info, skills, etc.
    // extracted by AWS services
    console.log('Structured data from AWS:', structuredData);
}

// Animate results sections in sequence
function animateResultsSections() {
    const sections = elements.resultsSection.querySelectorAll('.relative.group, .grid > .relative.group');

    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';

        setTimeout(() => {
            section.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

// 🎆 SPECTACULAR EFFECTS SYSTEM 🎆

// Setup spectacular effects
function setupSpectacularEffects() {
    // Add mouse trail effect
    setupMouseTrail();

    // Add button hover effects
    setupButtonEffects();

    // Add card hover effects
    setupCardEffects();

    // Add scroll animations
    setupScrollAnimations();
}

// Mouse trail effect
function setupMouseTrail() {
    const trail = [];
    const trailLength = 20;

    document.addEventListener('mousemove', (e) => {
        trail.push({ x: e.clientX, y: e.clientY, time: Date.now() });

        if (trail.length > trailLength) {
            trail.shift();
        }

        updateMouseTrail();
    });

    function updateMouseTrail() {
        // Remove old trail elements
        document.querySelectorAll('.mouse-trail').forEach(el => el.remove());

        trail.forEach((point, index) => {
            const trailElement = document.createElement('div');
            trailElement.className = 'mouse-trail';
            trailElement.style.cssText = `
                position: fixed;
                left: ${point.x}px;
                top: ${point.y}px;
                width: ${6 - (index * 0.3)}px;
                height: ${6 - (index * 0.3)}px;
                background: radial-gradient(circle, rgba(0, 255, 255, ${1 - (index * 0.05)}), transparent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                transform: translate(-50%, -50%);
                animation: trail-fade 0.5s ease-out forwards;
            `;

            document.body.appendChild(trailElement);

            setTimeout(() => {
                if (trailElement.parentNode) {
                    trailElement.parentNode.removeChild(trailElement);
                }
            }, 500);
        });
    }
}

// Enhanced button effects
function setupButtonEffects() {
    document.querySelectorAll('.btn-cyber, .btn-neon, .btn-holographic, .btn-glitch, .btn-matrix').forEach(button => {
        button.addEventListener('mouseenter', (e) => {
            createButtonRipple(e.target, e);
        });

        button.addEventListener('click', (e) => {
            createClickExplosion(e.target, e);
        });
    });
}

// Create button ripple effect
function createButtonRipple(button, event) {
    const rect = button.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const ripple = document.createElement('div');
    ripple.style.cssText = `
        position: absolute;
        left: ${x}px;
        top: ${y}px;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: ripple-expand 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
    `;

    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);

    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    }, 600);
}

// Create click explosion effect
function createClickExplosion(element, event) {
    const rect = element.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    for (let i = 0; i < 12; i++) {
        const particle = document.createElement('div');
        const angle = (i / 12) * Math.PI * 2;
        const velocity = 100 + Math.random() * 50;

        particle.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            width: 4px;
            height: 4px;
            background: var(--neon-cyan);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            box-shadow: 0 0 10px var(--neon-cyan);
        `;

        document.body.appendChild(particle);

        const deltaX = Math.cos(angle) * velocity;
        const deltaY = Math.sin(angle) * velocity;

        particle.animate([
            { transform: 'translate(-50%, -50%) scale(1)', opacity: 1 },
            { transform: `translate(${deltaX - 50}%, ${deltaY - 50}%) scale(0)`, opacity: 0 }
        ], {
            duration: 800,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }).onfinish = () => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        };
    }
}

// Enhanced card effects
function setupCardEffects() {
    document.querySelectorAll('.cyber-card, .holo-card, .neon-border-card, .matrix-card').forEach(card => {
        card.addEventListener('mouseenter', (e) => {
            createCardGlow(e.target);
        });

        card.addEventListener('mouseleave', (e) => {
            removeCardGlow(e.target);
        });

        card.addEventListener('mousemove', (e) => {
            updateCardTilt(e.target, e);
        });
    });
}

// Create card glow effect
function createCardGlow(card) {
    const glow = document.createElement('div');
    glow.className = 'card-glow';
    glow.style.cssText = `
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: var(--gradient-cyber);
        border-radius: inherit;
        opacity: 0;
        z-index: -1;
        filter: blur(20px);
        animation: glow-pulse 2s ease-in-out infinite;
    `;

    card.style.position = 'relative';
    card.appendChild(glow);

    glow.animate([
        { opacity: 0 },
        { opacity: 0.3 }
    ], {
        duration: 300,
        fill: 'forwards'
    });
}

// Remove card glow effect
function removeCardGlow(card) {
    const glow = card.querySelector('.card-glow');
    if (glow) {
        glow.animate([
            { opacity: 0.3 },
            { opacity: 0 }
        ], {
            duration: 300,
            fill: 'forwards'
        }).onfinish = () => {
            if (glow.parentNode) {
                glow.parentNode.removeChild(glow);
            }
        };
    }
}

// Update card tilt effect
function updateCardTilt(card, event) {
    const rect = card.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    const rotateX = (y - centerY) / centerY * -10;
    const rotateY = (x - centerX) / centerX * 10;

    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
}

// Setup interactive particles
function setupInteractiveParticles() {
    const particles = document.querySelectorAll('.particle');

    particles.forEach((particle, index) => {
        // Add random animation delays
        particle.style.animationDelay = `${Math.random() * 5}s`;

        // Add mouse interaction
        particle.addEventListener('mouseenter', () => {
            particle.style.transform = 'scale(2)';
            particle.style.opacity = '1';
        });

        particle.addEventListener('mouseleave', () => {
            particle.style.transform = 'scale(1)';
            particle.style.opacity = '0.8';
        });
    });
}

// Setup holographic effects
function setupHolographicEffects() {
    // Add holographic shimmer to buttons
    document.querySelectorAll('.btn-holographic').forEach(button => {
        button.addEventListener('mousemove', (e) => {
            const rect = button.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            button.style.background = `
                radial-gradient(circle at ${x}% ${y}%,
                    rgba(255, 255, 255, 0.3) 0%,
                    transparent 50%),
                var(--gradient-rainbow)
            `;
        });

        button.addEventListener('mouseleave', () => {
            button.style.background = 'var(--gradient-rainbow)';
        });
    });
}

// Setup scroll animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Add staggered animation for child elements
                const children = entry.target.querySelectorAll('.animate-child');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('animate-in');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    // Observe all cards and sections
    document.querySelectorAll('.cyber-card, .holo-card, .neon-border-card, .matrix-card, .glass-morphism').forEach(el => {
        observer.observe(el);
    });
}

// Welcome animation sequence
function triggerWelcomeAnimation() {
    // Animate header
    const header = document.querySelector('header');
    if (header) {
        header.style.transform = 'translateY(-100%)';
        header.style.opacity = '0';

        setTimeout(() => {
            header.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
            header.style.transform = 'translateY(0)';
            header.style.opacity = '1';
        }, 100);
    }

    // Animate hero section
    const hero = document.querySelector('.text-center.mb-16');
    if (hero) {
        hero.style.transform = 'translateY(50px)';
        hero.style.opacity = '0';

        setTimeout(() => {
            hero.style.transition = 'all 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
            hero.style.transform = 'translateY(0)';
            hero.style.opacity = '1';
        }, 300);
    }

    // Animate form
    const form = document.querySelector('#analysisForm');
    if (form) {
        form.style.transform = 'translateY(30px)';
        form.style.opacity = '0';

        setTimeout(() => {
            form.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
            form.style.transform = 'translateY(0)';
            form.style.opacity = '1';
        }, 600);
    }
}

// Show celebration animation
function showCelebration() {
    // Create celebration particles
    for (let i = 0; i < 50; i++) {
        createCelebrationParticle();
    }

    // Show celebration message
    const celebration = document.createElement('div');
    celebration.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--gradient-rainbow);
        color: white;
        padding: 20px 40px;
        border-radius: 20px;
        font-size: 24px;
        font-weight: bold;
        z-index: 10000;
        animation: celebration-bounce 2s ease-out;
        box-shadow: 0 0 50px rgba(255, 255, 255, 0.5);
    `;
    celebration.textContent = '🎉 Excellent Match! 🎉';

    document.body.appendChild(celebration);

    setTimeout(() => {
        if (celebration.parentNode) {
            celebration.parentNode.removeChild(celebration);
        }
    }, 3000);
}

// Create celebration particle
function createCelebrationParticle() {
    const particle = document.createElement('div');
    const colors = ['#ff0080', '#00ffff', '#ffff00', '#ff4500', '#00ff00'];
    const color = colors[Math.floor(Math.random() * colors.length)];

    particle.style.cssText = `
        position: fixed;
        left: ${Math.random() * 100}vw;
        top: 100vh;
        width: ${4 + Math.random() * 8}px;
        height: ${4 + Math.random() * 8}px;
        background: ${color};
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        box-shadow: 0 0 10px ${color};
    `;

    document.body.appendChild(particle);

    const duration = 3000 + Math.random() * 2000;
    const drift = (Math.random() - 0.5) * 200;

    particle.animate([
        { transform: 'translateY(0) translateX(0) rotate(0deg)', opacity: 1 },
        { transform: `translateY(-100vh) translateX(${drift}px) rotate(360deg)`, opacity: 0 }
    ], {
        duration: duration,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }).onfinish = () => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    };
}

// Show celebration animation
function showCelebration() {
    const celebration = document.createElement('div');
    celebration.innerHTML = '🎉';
    celebration.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-6xl z-50 animate-bounce';
    document.body.appendChild(celebration);

    setTimeout(() => {
        document.body.removeChild(celebration);
    }, 2000);
}

// 🎨 DYNAMIC THEME SYSTEM 🎨

// Setup theme selector
function setupThemeSelector() {
    const themeToggle = document.getElementById('themeToggle');
    const themeDropdown = document.getElementById('themeDropdown');
    const themeOptions = document.querySelectorAll('.theme-option');

    if (!themeToggle || !themeDropdown) return;

    // Load saved theme
    const savedTheme = localStorage.getItem('selectedTheme') || 'cyberpunk';
    applyTheme(savedTheme);

    // Toggle dropdown
    themeToggle.addEventListener('click', (e) => {
        e.stopPropagation();
        themeDropdown.classList.toggle('hidden');

        // Add animation
        if (!themeDropdown.classList.contains('hidden')) {
            themeDropdown.style.opacity = '0';
            themeDropdown.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                themeDropdown.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                themeDropdown.style.opacity = '1';
                themeDropdown.style.transform = 'translateY(0)';
            }, 10);
        }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', () => {
        if (themeDropdown) {
            themeDropdown.classList.add('hidden');
        }
    });

    // Theme selection
    themeOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            e.stopPropagation();
            const theme = option.dataset.theme;
            applyTheme(theme);
            localStorage.setItem('selectedTheme', theme);
            themeDropdown.classList.add('hidden');

            // Show theme change notification
            showThemeChangeNotification(theme);
        });
    });
}

// Apply theme
function applyTheme(theme) {
    // Remove existing theme
    document.documentElement.removeAttribute('data-theme');

    // Apply new theme
    if (theme !== 'cyberpunk') {
        document.documentElement.setAttribute('data-theme', theme);
    }

    // Update particles for new theme
    updateParticlesForTheme(theme);

    // Trigger theme transition animation
    triggerThemeTransition();
}

// Update particles for theme
function updateParticlesForTheme(theme) {
    const particles = document.querySelectorAll('.particle');

    particles.forEach((particle, index) => {
        // Add theme-specific classes
        particle.classList.remove('cyber', 'neon', 'electric', 'matrix', 'holo', 'theme-1', 'theme-2', 'accent');

        const particleTypes = ['cyber', 'neon', 'electric', 'matrix', 'holo', 'theme-1', 'theme-2', 'accent'];
        const randomType = particleTypes[index % particleTypes.length];
        particle.classList.add(randomType);

        // Add theme-specific animation delays
        particle.style.animationDelay = `${Math.random() * 5}s`;
    });
}

// Show theme change notification
function showThemeChangeNotification(theme) {
    const notification = document.createElement('div');
    notification.className = 'theme-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--gradient-primary);
        color: white;
        padding: 12px 20px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 600;
        z-index: 10000;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    `;

    const themeNames = {
        'cyberpunk': 'Cyberpunk',
        'ocean': 'Ocean Depths',
        'sunset': 'Sunset Vibes',
        'forest': 'Forest Mystique',
        'royal': 'Royal Purple',
        'aurora': 'Aurora Borealis',
        'cosmic': 'Cosmic Space'
    };

    notification.innerHTML = `
        <i class="fas fa-palette mr-2"></i>
        Theme changed to ${themeNames[theme]}
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Trigger theme transition animation
function triggerThemeTransition() {
    // Create transition overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: var(--gradient-primary);
        opacity: 0;
        z-index: 9999;
        pointer-events: none;
        transition: opacity 0.3s ease;
    `;

    document.body.appendChild(overlay);

    // Fade in
    setTimeout(() => {
        overlay.style.opacity = '0.3';
    }, 10);

    // Fade out and remove
    setTimeout(() => {
        overlay.style.opacity = '0';
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }, 200);

    // Add sparkle effects
    createThemeSparkles();
}

// Create sparkle effects for theme change
function createThemeSparkles() {
    for (let i = 0; i < 20; i++) {
        const sparkle = document.createElement('div');
        sparkle.style.cssText = `
            position: fixed;
            left: ${Math.random() * 100}vw;
            top: ${Math.random() * 100}vh;
            width: ${4 + Math.random() * 8}px;
            height: ${4 + Math.random() * 8}px;
            background: var(--primary-1);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9998;
            box-shadow: 0 0 10px var(--primary-1);
        `;

        document.body.appendChild(sparkle);

        const duration = 1000 + Math.random() * 1000;
        const scale = 0.5 + Math.random() * 1.5;

        sparkle.animate([
            {
                transform: 'scale(0) rotate(0deg)',
                opacity: 0
            },
            {
                transform: `scale(${scale}) rotate(180deg)`,
                opacity: 1,
                offset: 0.5
            },
            {
                transform: `scale(0) rotate(360deg)`,
                opacity: 0
            }
        ], {
            duration: duration,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }).onfinish = () => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        };
    }
}

// 🔐 AUTHENTICATION SYSTEM 🔐

// Initialize authentication
function initializeAuthentication() {
    // Check for stored auth token
    authToken = localStorage.getItem('authToken');
    const userInfo = localStorage.getItem('userInfo');

    if (authToken && userInfo) {
        try {
            currentUser = JSON.parse(userInfo);
            updateUIForAuthenticatedUser();
        } catch (e) {
            console.error('Error parsing stored user info:', e);
            logout();
        }
    } else {
        updateUIForGuestUser();
    }

    // Setup auth event listeners
    setupAuthEventListeners();
}

// Setup authentication event listeners
function setupAuthEventListeners() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }

    // Logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }

    // Auth modal toggles
    const loginBtn = document.getElementById('loginBtn');
    const registerBtn = document.getElementById('registerBtn');

    if (loginBtn) {
        loginBtn.addEventListener('click', () => showAuthModal('login'));
    }

    if (registerBtn) {
        registerBtn.addEventListener('click', () => showAuthModal('register'));
    }
}

// Handle user login
async function handleLogin(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const email = formData.get('email');
    const password = formData.get('password');

    if (!email || !password) {
        showNotification('Please enter both email and password', 'error');
        return;
    }

    try {
        showLoadingState('Signing in...');

        const response = await fetch(`${CONFIG.API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();

        if (response.ok) {
            // Store authentication data
            authToken = data.access_token;
            currentUser = {
                user_id: data.user_id,
                email: data.email,
                name: data.name
            };

            localStorage.setItem('authToken', authToken);
            localStorage.setItem('userInfo', JSON.stringify(currentUser));

            updateUIForAuthenticatedUser();
            hideAuthModal();
            showNotification(`Welcome back, ${data.name}!`, 'success');

        } else {
            showNotification(data.error || 'Login failed', 'error');
        }

    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed. Please try again.', 'error');
    } finally {
        hideLoadingState();
    }
}

// Handle user registration
async function handleRegister(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const name = formData.get('name');
    const email = formData.get('email');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    const company = formData.get('company');

    // Validation
    if (!name || !email || !password) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showNotification('Passwords do not match', 'error');
        return;
    }

    if (password.length < 8) {
        showNotification('Password must be at least 8 characters long', 'error');
        return;
    }

    try {
        showLoadingState('Creating account...');

        const response = await fetch(`${CONFIG.API_BASE_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, email, password, company })
        });

        const data = await response.json();

        if (response.ok) {
            showNotification('Account created successfully! Please log in.', 'success');
            showAuthModal('login');

            // Pre-fill login form
            const loginEmail = document.getElementById('loginEmail');
            if (loginEmail) {
                loginEmail.value = email;
            }

        } else {
            showNotification(data.error || 'Registration failed', 'error');
        }

    } catch (error) {
        console.error('Registration error:', error);
        showNotification('Registration failed. Please try again.', 'error');
    } finally {
        hideLoadingState();
    }
}

// Logout user
function logout() {
    authToken = null;
    currentUser = null;

    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');

    updateUIForGuestUser();
    showNotification('You have been logged out', 'info');
}

// Update UI for authenticated user
function updateUIForAuthenticatedUser() {
    // Hide auth buttons, show user menu
    const authButtons = document.querySelector('.auth-buttons');
    const userMenu = document.querySelector('.user-menu');

    if (authButtons) authButtons.style.display = 'none';
    if (userMenu) {
        userMenu.style.display = 'flex';

        // Update user name
        const userName = userMenu.querySelector('.user-name');
        if (userName && currentUser) {
            userName.textContent = currentUser.name;
        }
    }

    // Enable premium features
    enablePremiumFeatures();
}

// Update UI for guest user
function updateUIForGuestUser() {
    // Show auth buttons, hide user menu
    const authButtons = document.querySelector('.auth-buttons');
    const userMenu = document.querySelector('.user-menu');

    if (authButtons) authButtons.style.display = 'flex';
    if (userMenu) userMenu.style.display = 'none';

    // Disable premium features
    disablePremiumFeatures();
}

// Show authentication modal
function showAuthModal(mode = 'login') {
    let modal = document.getElementById('authModal');

    if (!modal) {
        modal = createAuthModal();
        document.body.appendChild(modal);
    }

    // Switch between login and register
    const loginForm = modal.querySelector('#loginForm');
    const registerForm = modal.querySelector('#registerForm');
    const modalTitle = modal.querySelector('.modal-title');

    if (mode === 'login') {
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
        modalTitle.textContent = 'Sign In';
    } else {
        loginForm.style.display = 'none';
        registerForm.style.display = 'block';
        modalTitle.textContent = 'Create Account';
    }

    modal.style.display = 'flex';

    // Add animation
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// Hide authentication modal
function hideAuthModal() {
    const modal = document.getElementById('authModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// Create authentication modal
function createAuthModal() {
    const modal = document.createElement('div');
    modal.id = 'authModal';
    modal.className = 'auth-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="hideAuthModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Sign In</h2>
                <button class="modal-close" onclick="hideAuthModal()">×</button>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <input type="email" id="loginEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit" class="btn-cyber">Sign In</button>
                <p class="auth-switch">
                    Don't have an account?
                    <a href="#" onclick="showAuthModal('register')">Create one</a>
                </p>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="auth-form" style="display: none;">
                <div class="form-group">
                    <label for="registerName">Full Name</label>
                    <input type="text" id="registerName" name="name" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <input type="email" id="registerEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="registerCompany">Company (Optional)</label>
                    <input type="text" id="registerCompany" name="company">
                </div>
                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <input type="password" id="registerPassword" name="password" required minlength="8">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                <button type="submit" class="btn-cyber">Create Account</button>
                <p class="auth-switch">
                    Already have an account?
                    <a href="#" onclick="showAuthModal('login')">Sign in</a>
                </p>
            </form>
        </div>
    `;

    return modal;
}

// Enable premium features for authenticated users
function enablePremiumFeatures() {
    // Enable A2I human review
    const a2iOption = document.getElementById('enableA2IReview');
    if (a2iOption) {
        a2iOption.disabled = false;
        a2iOption.parentElement.classList.remove('disabled');
    }

    // Enable file history
    const fileHistory = document.querySelector('.file-history');
    if (fileHistory) {
        fileHistory.style.display = 'block';
        loadUserFiles();
    }

    // Enable advanced analysis options
    const advancedOptions = document.querySelector('.advanced-options');
    if (advancedOptions) {
        advancedOptions.style.display = 'block';
    }
}

// Disable premium features for guest users
function disablePremiumFeatures() {
    // Disable A2I human review
    const a2iOption = document.getElementById('enableA2IReview');
    if (a2iOption) {
        a2iOption.disabled = true;
        a2iOption.checked = false;
        a2iOption.parentElement.classList.add('disabled');
    }

    // Hide file history
    const fileHistory = document.querySelector('.file-history');
    if (fileHistory) {
        fileHistory.style.display = 'none';
    }

    // Hide advanced options
    const advancedOptions = document.querySelector('.advanced-options');
    if (advancedOptions) {
        advancedOptions.style.display = 'none';
    }
}

// Load user's uploaded files
async function loadUserFiles() {
    if (!authToken) return;

    try {
        const response = await fetch(`${CONFIG.API_BASE_URL}/files`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayUserFiles(data.files);
        }

    } catch (error) {
        console.error('Error loading user files:', error);
    }
}

// Display user files in the UI
function displayUserFiles(files) {
    const fileList = document.getElementById('userFilesList');
    if (!fileList) return;

    if (files.length === 0) {
        fileList.innerHTML = '<p class="no-files">No files uploaded yet</p>';
        return;
    }

    fileList.innerHTML = files.map(file => `
        <div class="file-item" data-file-id="${file.file_id}">
            <div class="file-info">
                <span class="file-name">${file.filename}</span>
                <span class="file-date">${new Date(file.uploaded_at).toLocaleDateString()}</span>
                <span class="file-size">${formatFileSize(file.size)}</span>
            </div>
            <div class="file-actions">
                <button onclick="useExistingFile('${file.file_id}')" class="btn-small btn-neon">Use</button>
                <button onclick="deleteFile('${file.file_id}')" class="btn-small btn-danger">Delete</button>
            </div>
        </div>
    `).join('');
}

// Format file size for display
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Use existing file for analysis
async function useExistingFile(fileId) {
    if (!authToken) return;

    try {
        const response = await fetch(`${CONFIG.API_BASE_URL}/files/${fileId}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const fileData = await response.json();

            // Set the analysis ID to use this file
            elements.analysisIdInput.value = fileData.file_id;

            // Update UI to show selected file
            showNotification(`Selected file: ${fileData.filename}`, 'success');

            // Scroll to analysis section
            document.getElementById('analysisForm').scrollIntoView({ behavior: 'smooth' });
        }

    } catch (error) {
        console.error('Error using existing file:', error);
        showNotification('Error selecting file', 'error');
    }
}

// Delete user file
async function deleteFile(fileId) {
    if (!authToken) return;

    if (!confirm('Are you sure you want to delete this file?')) {
        return;
    }

    try {
        const response = await fetch(`${CONFIG.API_BASE_URL}/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            showNotification('File deleted successfully', 'success');
            loadUserFiles(); // Refresh the list
        } else {
            showNotification('Error deleting file', 'error');
        }

    } catch (error) {
        console.error('Error deleting file:', error);
        showNotification('Error deleting file', 'error');
    }
}

// Enhanced upload function with authentication
async function uploadFileEnhanced(file) {
    const fileData = await fileToBase64(file);

    const uploadData = {
        filename: file.name,
        content: fileData,
        content_type: file.type,
        user_id: currentUser ? currentUser.user_id : 'anonymous'
    };

    const headers = {
        'Content-Type': 'application/json'
    };

    if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
    }

    const response = await fetch(`${CONFIG.API_BASE_URL}/upload`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(uploadData)
    });

    return response;
}

// Enhanced analysis function with authentication and A2I
async function performEnhancedAnalysis(analysisId, jobDescription) {
    const analysisData = {
        analysis_id: analysisId,
        job_description: jobDescription,
        user_id: currentUser ? currentUser.user_id : 'anonymous',
        enable_a2i_review: document.getElementById('enableA2IReview')?.checked || false
    };

    const headers = {
        'Content-Type': 'application/json'
    };

    if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
    }

    const response = await fetch(`${CONFIG.API_BASE_URL}/analyze`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(analysisData)
    });

    return response;
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

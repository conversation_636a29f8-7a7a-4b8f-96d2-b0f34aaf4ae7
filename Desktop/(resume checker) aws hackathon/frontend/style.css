/* Custom styles for Resume Match Analyzer */

/* Global styles and animations */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom font loading */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap');

/* Advanced keyframe animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes glow {
    0%, 100% { text-shadow: 0 0 20px rgba(59, 130, 246, 0.5), 0 0 30px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1); }
    50% { text-shadow: 0 0 30px rgba(147, 51, 234, 0.6), 0 0 40px rgba(147, 51, 234, 0.4), 0 0 50px rgba(147, 51, 234, 0.2); }
}

@keyframes shimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
    50% { box-shadow: 0 0 40px rgba(147, 51, 234, 0.6), 0 0 60px rgba(147, 51, 234, 0.3); }
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes particle-float {
    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.7; }
    25% { transform: translateY(-100px) translateX(50px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-200px) translateX(-30px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-150px) translateX(-80px) rotate(270deg); opacity: 0.9; }
}

@keyframes matrix-rain {
    0% { transform: translateY(-100vh); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes neon-flicker {
    0%, 100% { opacity: 1; text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor; }
    50% { opacity: 0.8; text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor; }
}

@keyframes hologram {
    0%, 100% {
        background-position: 0% 0%;
        filter: hue-rotate(0deg);
    }
    25% {
        background-position: 100% 0%;
        filter: hue-rotate(90deg);
    }
    50% {
        background-position: 100% 100%;
        filter: hue-rotate(180deg);
    }
    75% {
        background-position: 0% 100%;
        filter: hue-rotate(270deg);
    }
}

/* Background patterns and textures */
.bg-grid-pattern {
    background-image:
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: matrix-rain 20s linear infinite;
}

/* Floating particles */
.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
    border-radius: 50%;
    animation: particle-float 15s infinite linear;
    opacity: 0.7;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 12s; }
.particle:nth-child(2) { left: 20%; animation-delay: 2s; animation-duration: 15s; }
.particle:nth-child(3) { left: 30%; animation-delay: 4s; animation-duration: 18s; }
.particle:nth-child(4) { left: 40%; animation-delay: 6s; animation-duration: 14s; }
.particle:nth-child(5) { left: 50%; animation-delay: 8s; animation-duration: 16s; }
.particle:nth-child(6) { left: 60%; animation-delay: 10s; animation-duration: 13s; }
.particle:nth-child(7) { left: 70%; animation-delay: 12s; animation-duration: 17s; }
.particle:nth-child(8) { left: 80%; animation-delay: 14s; animation-duration: 19s; }

/* Glass morphism effects */
.glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Neon glow effects */
.neon-blue {
    color: #3b82f6;
    text-shadow:
        0 0 5px #3b82f6,
        0 0 10px #3b82f6,
        0 0 15px #3b82f6,
        0 0 20px #3b82f6;
    animation: neon-flicker 3s ease-in-out infinite;
}

.neon-purple {
    color: #8b5cf6;
    text-shadow:
        0 0 5px #8b5cf6,
        0 0 10px #8b5cf6,
        0 0 15px #8b5cf6,
        0 0 20px #8b5cf6;
    animation: neon-flicker 3s ease-in-out infinite;
    animation-delay: 1s;
}

.neon-pink {
    color: #ec4899;
    text-shadow:
        0 0 5px #ec4899,
        0 0 10px #ec4899,
        0 0 15px #ec4899,
        0 0 20px #ec4899;
    animation: neon-flicker 3s ease-in-out infinite;
    animation-delay: 2s;
}

/* Holographic text effect */
.holographic {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b, #10b981);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: hologram 4s ease-in-out infinite;
}

/* Advanced button effects */
.btn-holographic {
    position: relative;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
    background-size: 300% 300%;
    animation: gradient-shift 3s ease infinite;
    border: none;
    border-radius: 12px;
    padding: 16px 32px;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    overflow: hidden;
    cursor: pointer;
    transform: perspective(1000px) rotateX(0deg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-holographic::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-holographic:hover::before {
    left: 100%;
}

.btn-holographic:hover {
    transform: perspective(1000px) rotateX(10deg) translateY(-5px);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.3),
        0 0 30px rgba(59, 130, 246, 0.5),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

.btn-holographic:active {
    transform: perspective(1000px) rotateX(5deg) translateY(-2px);
}

/* Cyberpunk grid overlay */
.cyber-grid {
    position: relative;
    overflow: hidden;
}

.cyber-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: matrix-rain 30s linear infinite;
    pointer-events: none;
}

/* Enhanced drop zone with cyberpunk styling */
#dropZone {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 2px dashed rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#dropZone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

#dropZone:hover {
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.2),
        0 0 30px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255,255,255,0.1);
}

#dropZone:hover::before {
    left: 100%;
}

#dropZone.dragover {
    border-color: rgba(139, 92, 246, 0.8);
    background: rgba(139, 92, 246, 0.2);
    transform: scale(1.02);
    box-shadow:
        0 25px 50px rgba(0,0,0,0.3),
        0 0 50px rgba(139, 92, 246, 0.5),
        inset 0 1px 0 rgba(255,255,255,0.2);
}

/* Futuristic form inputs */
textarea, input[type="text"] {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 12px !important;
    color: white !important;
    padding: 16px !important;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

textarea:focus, input[type="text"]:focus {
    outline: none !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    background: rgba(59, 130, 246, 0.1) !important;
    box-shadow:
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px);
}

textarea::placeholder, input::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Animated progress bars */
.progress-bar-fill {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    background-size: 200% 100%;
    animation: gradient-shift 2s ease infinite;
    position: relative;
    overflow: hidden;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 1.5s ease infinite;
}

/* Step indicators with advanced styling */
.step-number, .step-check {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#step1.completed .step-number,
#step2.completed .step-number,
#step3.completed .step-number {
    opacity: 0;
    transform: scale(0);
}

#step1.completed .step-check,
#step2.completed .step-check,
#step3.completed .step-check {
    opacity: 1;
    transform: scale(1);
    animation: pulse-glow 2s ease infinite;
}

/* Enhanced list item styles */
.strength-item {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    backdrop-filter: blur(10px);
}

.strength-item:hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.2) 100%);
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15);
}

.missing-skill-item {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%);
    border: 1px solid rgba(249, 115, 22, 0.2);
    backdrop-filter: blur(10px);
}

.missing-skill-item:hover {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.2) 0%, rgba(239, 68, 68, 0.2) 100%);
    border-color: rgba(249, 115, 22, 0.4);
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
}

.recommendation-item {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
}

.recommendation-item:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Celebration animation */
@keyframes celebration {
    0% { transform: scale(0) rotate(0deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
    100% { transform: scale(1) rotate(360deg); opacity: 1; }
}

.celebration {
    animation: celebration 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Confetti animation */
@keyframes confetti-fall {
    0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
    animation: confetti-fall 3s linear infinite;
    z-index: 1000;
}

/* Enhanced footer */
footer {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .particles {
        display: none; /* Hide particles on mobile for performance */
    }

    .bg-grid-pattern {
        opacity: 0.05; /* Reduce grid opacity on mobile */
    }

    /* Simplify animations on mobile */
    .animate-float,
    .animate-shimmer,
    .animate-glow {
        animation: none;
    }

    /* Reduce blur effects on mobile */
    .backdrop-blur-2xl {
        backdrop-filter: blur(10px);
    }

    .backdrop-blur-xl {
        backdrop-filter: blur(8px);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-white\/10 {
        background: rgba(255, 255, 255, 0.2);
    }

    .border-white\/20 {
        border-color: rgba(255, 255, 255, 0.4);
    }

    .text-blue-200 {
        color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .animate-spin,
    .animate-pulse,
    .animate-bounce,
    .animate-float,
    .animate-shimmer,
    .animate-glow {
        animation: none;
    }
}

/* Print styles */
@media print {
    .particles,
    .bg-grid-pattern,
    .backdrop-blur-2xl,
    .backdrop-blur-xl {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .bg-gradient-to-r,
    .bg-gradient-to-br {
        background: white;
    }

    .text-white {
        color: black;
    }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Selection styling */
::selection {
    background: rgba(59, 130, 246, 0.3);
    color: white;
}

::-moz-selection {
    background: rgba(59, 130, 246, 0.3);
    color: white;
}

/* Enhanced tab styles with cyberpunk theme */
.upload-tab {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px 8px 0 0;
    padding: 12px 24px;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.upload-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    transition: left 0.5s;
}

.upload-tab:hover::before {
    left: 100%;
}

.upload-tab.active {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.5);
    color: #60a5fa !important;
    box-shadow:
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.upload-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    animation: shimmer 2s ease infinite;
}

.upload-tab:hover {
    border-bottom-color: #9ca3af !important;
    color: #374151 !important;
}

/* Upload content transitions */
.upload-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced drop zone */
#dropZone {
    position: relative;
    overflow: hidden;
}

#dropZone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

#dropZone:hover::before {
    left: 100%;
}

/* Progress steps animation */
.progress-step {
    transition: all 0.3s ease-in-out;
}

.progress-step.completed .step-circle {
    background-color: #3b82f6;
    color: white;
    transform: scale(1.1);
}

.progress-step.completed .step-text {
    color: #3b82f6;
    font-weight: 600;
}

/* Button enhancements */
button {
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

button:active::before {
    width: 300px;
    height: 300px;
}

/* Gradient button */
.gradient-button {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Toast notifications */
.toast {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Enhanced loading animation */
.loading-brain {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Character counter styling */
.character-counter {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-indicator {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Drop zone hover effects */
#dropZone.dragover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* File upload animations */
#fileInfo {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Results animation */
#resultsSection {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Match score circle animation */
#matchScoreCircle {
    transition: stroke-dasharray 1s ease-in-out;
}

/* Custom button hover effects */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active {
    transform: translateY(0);
}

/* Form input focus effects */
input:focus,
textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* List item animations */
.list-item {
    animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Strength items */
.strength-item {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-left: 4px solid #10b981;
}

/* Missing skill items */
.missing-skill-item {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left: 4px solid #ef4444;
}

/* Recommendation items */
.recommendation-item {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-left: 4px solid #3b82f6;
}

/* Status indicators */
.status-met {
    color: #10b981;
}

.status-not-met {
    color: #ef4444;
}

.status-partial {
    color: #f59e0b;
}

/* Card hover effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Progress bar styles */
.progress-bar {
    background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
    
    h1 {
        font-size: 1.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .text-4xl {
        font-size: 2.5rem;
    }
}

/* Print styles */
@media print {
    header,
    footer,
    #analysisForm,
    #newAnalysisBtn {
        display: none;
    }
    
    #resultsSection {
        display: block !important;
    }
    
    .shadow-lg {
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    /* Add dark mode styles if needed */
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators for keyboard navigation */
button:focus,
input:focus,
textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .border-gray-300 {
        border-color: #000;
    }
    
    .text-gray-600 {
        color: #000;
    }
}

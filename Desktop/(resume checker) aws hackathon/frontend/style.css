/* Custom styles for Resume Match Analyzer */

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.2s ease-in-out;
}

/* Tab styles */
.upload-tab.active {
    border-bottom-color: #3b82f6 !important;
    color: #3b82f6 !important;
}

.upload-tab:hover {
    border-bottom-color: #9ca3af !important;
    color: #374151 !important;
}

/* Upload content transitions */
.upload-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced drop zone */
#dropZone {
    position: relative;
    overflow: hidden;
}

#dropZone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

#dropZone:hover::before {
    left: 100%;
}

/* Progress steps animation */
.progress-step {
    transition: all 0.3s ease-in-out;
}

.progress-step.completed .step-circle {
    background-color: #3b82f6;
    color: white;
    transform: scale(1.1);
}

.progress-step.completed .step-text {
    color: #3b82f6;
    font-weight: 600;
}

/* Button enhancements */
button {
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

button:active::before {
    width: 300px;
    height: 300px;
}

/* Gradient button */
.gradient-button {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Toast notifications */
.toast {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Enhanced loading animation */
.loading-brain {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Character counter styling */
.character-counter {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-indicator {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Drop zone hover effects */
#dropZone.dragover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* File upload animations */
#fileInfo {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Results animation */
#resultsSection {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Match score circle animation */
#matchScoreCircle {
    transition: stroke-dasharray 1s ease-in-out;
}

/* Custom button hover effects */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active {
    transform: translateY(0);
}

/* Form input focus effects */
input:focus,
textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* List item animations */
.list-item {
    animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Strength items */
.strength-item {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-left: 4px solid #10b981;
}

/* Missing skill items */
.missing-skill-item {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left: 4px solid #ef4444;
}

/* Recommendation items */
.recommendation-item {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-left: 4px solid #3b82f6;
}

/* Status indicators */
.status-met {
    color: #10b981;
}

.status-not-met {
    color: #ef4444;
}

.status-partial {
    color: #f59e0b;
}

/* Card hover effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Progress bar styles */
.progress-bar {
    background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
    
    h1 {
        font-size: 1.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .text-4xl {
        font-size: 2.5rem;
    }
}

/* Print styles */
@media print {
    header,
    footer,
    #analysisForm,
    #newAnalysisBtn {
        display: none;
    }
    
    #resultsSection {
        display: block !important;
    }
    
    .shadow-lg {
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    /* Add dark mode styles if needed */
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators for keyboard navigation */
button:focus,
input:focus,
textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .border-gray-300 {
        border-color: #000;
    }
    
    .text-gray-600 {
        color: #000;
    }
}

/* 🚀 ULTRA-ENHANCED CYBERPUNK RESUME ANALYZER STYLES 🚀 */

/* Enhanced Font Loading */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap');

/* 🎨 DYNAMIC MULTI-THEME COLOR SYSTEM 🎨 */

/* Theme 1: Ocean Depths */
:root[data-theme="ocean"] {
    --primary-1: #0077be;      /* Deep Ocean Blue */
    --primary-2: #00a8cc;      /* Turquoise */
    --primary-3: #40e0d0;      /* Bright Turquoise */
    --secondary-1: #1e3a8a;    /* Navy Blue */
    --secondary-2: #3b82f6;    /* Sky Blue */
    --accent-1: #06d6a0;       /* Sea Green */
    --accent-2: #ffd60a;       /* Golden Yellow */
    --accent-3: #f72585;       /* Coral Pink */

    --gradient-primary: linear-gradient(135deg, #0077be, #00a8cc, #40e0d0);
    --gradient-secondary: linear-gradient(135deg, #1e3a8a, #3b82f6, #06d6a0);
    --gradient-accent: linear-gradient(135deg, #06d6a0, #ffd60a, #f72585);
    --gradient-rainbow: linear-gradient(135deg, #0077be, #00a8cc, #40e0d0, #06d6a0, #ffd60a);
}

/* Theme 2: Sunset Vibes */
:root[data-theme="sunset"] {
    --primary-1: #ff6b35;      /* Vibrant Orange */
    --primary-2: #f7931e;      /* Golden Orange */
    --primary-3: #ffbe0b;      /* Bright Yellow */
    --secondary-1: #8338ec;    /* Purple */
    --secondary-2: #3a86ff;    /* Blue */
    --accent-1: #fb5607;       /* Red Orange */
    --accent-2: #ff006e;       /* Hot Pink */
    --accent-3: #8b5cf6;       /* Violet */

    --gradient-primary: linear-gradient(135deg, #ff6b35, #f7931e, #ffbe0b);
    --gradient-secondary: linear-gradient(135deg, #8338ec, #3a86ff, #fb5607);
    --gradient-accent: linear-gradient(135deg, #fb5607, #ff006e, #8b5cf6);
    --gradient-rainbow: linear-gradient(135deg, #ff6b35, #f7931e, #ffbe0b, #8338ec, #ff006e);
}

/* Theme 3: Forest Mystique */
:root[data-theme="forest"] {
    --primary-1: #2d5016;      /* Dark Forest Green */
    --primary-2: #52b788;      /* Medium Green */
    --primary-3: #95d5b2;      /* Light Green */
    --secondary-1: #1b4332;    /* Deep Forest */
    --secondary-2: #40916c;    /* Emerald */
    --accent-1: #f1c0e8;       /* Soft Pink */
    --accent-2: #cfbaf0;       /* Lavender */
    --accent-3: #a3c4f3;       /* Sky Blue */

    --gradient-primary: linear-gradient(135deg, #2d5016, #52b788, #95d5b2);
    --gradient-secondary: linear-gradient(135deg, #1b4332, #40916c, #52b788);
    --gradient-accent: linear-gradient(135deg, #f1c0e8, #cfbaf0, #a3c4f3);
    --gradient-rainbow: linear-gradient(135deg, #2d5016, #52b788, #95d5b2, #f1c0e8, #cfbaf0);
}

/* Theme 4: Royal Purple */
:root[data-theme="royal"] {
    --primary-1: #4c1d95;      /* Deep Purple */
    --primary-2: #7c3aed;      /* Purple */
    --primary-3: #a855f7;      /* Light Purple */
    --secondary-1: #1e1b4b;    /* Dark Indigo */
    --secondary-2: #3730a3;    /* Indigo */
    --accent-1: #f59e0b;       /* Amber */
    --accent-2: #ef4444;       /* Red */
    --accent-3: #10b981;       /* Emerald */

    --gradient-primary: linear-gradient(135deg, #4c1d95, #7c3aed, #a855f7);
    --gradient-secondary: linear-gradient(135deg, #1e1b4b, #3730a3, #7c3aed);
    --gradient-accent: linear-gradient(135deg, #f59e0b, #ef4444, #10b981);
    --gradient-rainbow: linear-gradient(135deg, #4c1d95, #7c3aed, #a855f7, #f59e0b, #ef4444);
}

/* Theme 5: Aurora Borealis */
:root[data-theme="aurora"] {
    --primary-1: #065f46;      /* Dark Teal */
    --primary-2: #059669;      /* Teal */
    --primary-3: #34d399;      /* Light Teal */
    --secondary-1: #1e40af;    /* Blue */
    --secondary-2: #3b82f6;    /* Light Blue */
    --accent-1: #a78bfa;       /* Purple */
    --accent-2: #f472b6;       /* Pink */
    --accent-3: #fbbf24;       /* Yellow */

    --gradient-primary: linear-gradient(135deg, #065f46, #059669, #34d399);
    --gradient-secondary: linear-gradient(135deg, #1e40af, #3b82f6, #059669);
    --gradient-accent: linear-gradient(135deg, #a78bfa, #f472b6, #fbbf24);
    --gradient-rainbow: linear-gradient(135deg, #065f46, #059669, #34d399, #a78bfa, #f472b6);
}

/* Theme 6: Cosmic Space */
:root[data-theme="cosmic"] {
    --primary-1: #1a1a2e;      /* Deep Space */
    --primary-2: #16213e;      /* Dark Blue */
    --primary-3: #0f3460;      /* Space Blue */
    --secondary-1: #533483;    /* Purple */
    --secondary-2: #7209b7;    /* Bright Purple */
    --accent-1: #f72585;       /* Hot Pink */
    --accent-2: #4cc9f0;       /* Cyan */
    --accent-3: #7209b7;       /* Electric Purple */

    --gradient-primary: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    --gradient-secondary: linear-gradient(135deg, #533483, #7209b7, #f72585);
    --gradient-accent: linear-gradient(135deg, #f72585, #4cc9f0, #7209b7);
    --gradient-rainbow: linear-gradient(135deg, #1a1a2e, #533483, #7209b7, #f72585, #4cc9f0);
}

/* Default Theme: Cyberpunk (Original) */
:root {
    --primary-1: #00ffff;      /* Neon Cyan */
    --primary-2: #ff00ff;      /* Neon Magenta */
    --primary-3: #ffff00;      /* Neon Yellow */
    --secondary-1: #0080ff;    /* Electric Blue */
    --secondary-2: #8a2be2;    /* Blue Violet */
    --accent-1: #00ff00;       /* Neon Green */
    --accent-2: #ff4500;       /* Orange Red */
    --accent-3: #ff1493;       /* Deep Pink */

    --gradient-primary: linear-gradient(135deg, var(--primary-1), var(--primary-2), var(--primary-3));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-1), var(--secondary-2), var(--accent-1));
    --gradient-accent: linear-gradient(135deg, var(--accent-1), var(--accent-2), var(--accent-3));
    --gradient-rainbow: linear-gradient(135deg, #00ffff, #ff00ff, #ffff00, #00ff00, #ff4500, #ff1493);

    /* Animation Timings */
    --speed-ultra-fast: 0.1s;
    --speed-fast: 0.3s;
    --speed-medium: 0.6s;
    --speed-slow: 1.2s;
    --speed-ultra-slow: 2.4s;
}

/* Global Enhanced Animations */
* {
    transition: all var(--speed-fast) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 🎨 SPECTACULAR KEYFRAME ANIMATIONS 🎨 */

/* Enhanced Float Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
    33% { transform: translateY(-20px) rotate(1deg) scale(1.02); }
    66% { transform: translateY(-10px) rotate(-1deg) scale(0.98); }
}

/* Cyber Pulse Animation */
@keyframes cyber-pulse {
    0%, 100% {
        box-shadow:
            0 0 20px var(--neon-cyan),
            0 0 40px var(--neon-cyan),
            0 0 60px var(--neon-cyan),
            inset 0 0 20px rgba(0, 255, 255, 0.1);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 30px var(--neon-magenta),
            0 0 60px var(--neon-magenta),
            0 0 90px var(--neon-magenta),
            inset 0 0 30px rgba(255, 0, 255, 0.2);
        transform: scale(1.05);
    }
}

/* Electric Border Animation */
@keyframes electric-border {
    0% {
        border-color: var(--neon-cyan);
        box-shadow: 0 0 20px var(--neon-cyan);
    }
    25% {
        border-color: var(--neon-magenta);
        box-shadow: 0 0 20px var(--neon-magenta);
    }
    50% {
        border-color: var(--neon-yellow);
        box-shadow: 0 0 20px var(--neon-yellow);
    }
    75% {
        border-color: var(--neon-green);
        box-shadow: 0 0 20px var(--neon-green);
    }
    100% {
        border-color: var(--neon-cyan);
        box-shadow: 0 0 20px var(--neon-cyan);
    }
}

/* Matrix Digital Rain */
@keyframes matrix-digital-rain {
    0% {
        transform: translateY(-100vh) rotateX(0deg);
        opacity: 0;
        color: var(--neon-green);
    }
    10% {
        opacity: 1;
        color: var(--neon-cyan);
    }
    50% {
        color: var(--neon-green);
        transform: translateY(50vh) rotateX(180deg);
    }
    90% {
        opacity: 1;
        color: var(--neon-cyan);
    }
    100% {
        transform: translateY(100vh) rotateX(360deg);
        opacity: 0;
        color: var(--neon-green);
    }
}

/* Holographic Shimmer */
@keyframes holographic-shimmer {
    0% {
        background-position: -200% center;
        filter: hue-rotate(0deg) brightness(1);
    }
    50% {
        filter: hue-rotate(180deg) brightness(1.2);
    }
    100% {
        background-position: 200% center;
        filter: hue-rotate(360deg) brightness(1);
    }
}

/* Neon Text Flicker */
@keyframes neon-text-flicker {
    0%, 100% {
        text-shadow:
            0 0 5px currentColor,
            0 0 10px currentColor,
            0 0 15px currentColor,
            0 0 20px currentColor,
            0 0 35px currentColor;
        opacity: 1;
    }
    2%, 8%, 12%, 18%, 22%, 28%, 32%, 38%, 42%, 48%, 52%, 58%, 62%, 68%, 72%, 78%, 82%, 88%, 92%, 98% {
        text-shadow:
            0 0 2px currentColor,
            0 0 5px currentColor,
            0 0 8px currentColor,
            0 0 12px currentColor,
            0 0 20px currentColor;
        opacity: 0.8;
    }
    4%, 16%, 24%, 36%, 44%, 56%, 64%, 76%, 84%, 96% {
        text-shadow:
            0 0 1px currentColor,
            0 0 3px currentColor,
            0 0 5px currentColor,
            0 0 8px currentColor,
            0 0 15px currentColor;
        opacity: 0.6;
    }
}

/* Cyber Glitch Effect */
@keyframes cyber-glitch {
    0%, 100% {
        transform: translate(0);
        filter: hue-rotate(0deg);
    }
    10% {
        transform: translate(-2px, 2px);
        filter: hue-rotate(90deg);
    }
    20% {
        transform: translate(-4px, -2px);
        filter: hue-rotate(180deg);
    }
    30% {
        transform: translate(4px, 2px);
        filter: hue-rotate(270deg);
    }
    40% {
        transform: translate(-2px, -4px);
        filter: hue-rotate(360deg);
    }
    50% {
        transform: translate(2px, 4px);
        filter: hue-rotate(90deg);
    }
    60% {
        transform: translate(-4px, 2px);
        filter: hue-rotate(180deg);
    }
    70% {
        transform: translate(4px, -2px);
        filter: hue-rotate(270deg);
    }
    80% {
        transform: translate(-2px, 4px);
        filter: hue-rotate(360deg);
    }
    90% {
        transform: translate(2px, -4px);
        filter: hue-rotate(90deg);
    }
}

@keyframes glow {
    0%, 100% { text-shadow: 0 0 20px rgba(59, 130, 246, 0.5), 0 0 30px rgba(59, 130, 246, 0.3), 0 0 40px rgba(59, 130, 246, 0.1); }
    50% { text-shadow: 0 0 30px rgba(147, 51, 234, 0.6), 0 0 40px rgba(147, 51, 234, 0.4), 0 0 50px rgba(147, 51, 234, 0.2); }
}

@keyframes shimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
    50% { box-shadow: 0 0 40px rgba(147, 51, 234, 0.6), 0 0 60px rgba(147, 51, 234, 0.3); }
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes particle-float {
    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.7; }
    25% { transform: translateY(-100px) translateX(50px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-200px) translateX(-30px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-150px) translateX(-80px) rotate(270deg); opacity: 0.9; }
}

@keyframes matrix-rain {
    0% { transform: translateY(-100vh); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes neon-flicker {
    0%, 100% { opacity: 1; text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor; }
    50% { opacity: 0.8; text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor; }
}

@keyframes hologram {
    0%, 100% {
        background-position: 0% 0%;
        filter: hue-rotate(0deg);
    }
    25% {
        background-position: 100% 0%;
        filter: hue-rotate(90deg);
    }
    50% {
        background-position: 100% 100%;
        filter: hue-rotate(180deg);
    }
    75% {
        background-position: 0% 100%;
        filter: hue-rotate(270deg);
    }
}

/* Background patterns and textures */
.bg-grid-pattern {
    background-image:
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: matrix-rain 20s linear infinite;
}

/* 🌌 SPECTACULAR PARTICLE SYSTEMS 🌌 */

/* Enhanced Floating Particles */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    border-radius: 50%;
    animation: particle-float 15s infinite linear;
    opacity: 0.8;
    filter: blur(0.5px);
}

/* Enhanced Particles with Dynamic Colors */
.particle.cyber {
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, var(--primary-1), transparent);
    box-shadow: 0 0 10px var(--primary-1);
}

.particle.neon {
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, var(--primary-2), transparent);
    box-shadow: 0 0 8px var(--primary-2);
}

.particle.electric {
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, var(--primary-3), var(--accent-2), transparent);
    box-shadow: 0 0 15px var(--primary-3);
}

.particle.matrix {
    width: 3px;
    height: 3px;
    background: var(--accent-1);
    box-shadow: 0 0 6px var(--accent-1);
}

.particle.holo {
    width: 5px;
    height: 5px;
    background: var(--gradient-rainbow);
    background-size: 200% 200%;
    animation: particle-float 20s infinite linear, holographic-shimmer 3s ease infinite;
}

/* Theme-specific Particles */
.particle.theme-1 {
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, var(--secondary-1), transparent);
    box-shadow: 0 0 12px var(--secondary-1);
}

.particle.theme-2 {
    width: 5px;
    height: 5px;
    background: radial-gradient(circle, var(--secondary-2), transparent);
    box-shadow: 0 0 10px var(--secondary-2);
}

.particle.accent {
    width: 7px;
    height: 7px;
    background: radial-gradient(circle, var(--accent-3), transparent);
    box-shadow: 0 0 14px var(--accent-3);
}

/* Enhanced Particle Animations */
@keyframes particle-cyber-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-150px) translateX(75px) rotate(90deg) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-300px) translateX(-50px) rotate(180deg) scale(0.8);
        opacity: 0.9;
    }
    75% {
        transform: translateY(-200px) translateX(-100px) rotate(270deg) scale(1.1);
        opacity: 0.7;
    }
}

/* 🎆 SPECTACULAR ANIMATION LIBRARY 🎆 */

/* Ripple Effect */
@keyframes ripple-expand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
    }
}

/* Trail Fade */
@keyframes trail-fade {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
    }
}

/* Glow Pulse */
@keyframes glow-pulse {
    0%, 100% {
        filter: blur(20px);
        opacity: 0.3;
    }
    50% {
        filter: blur(25px);
        opacity: 0.6;
    }
}

/* Celebration Bounce */
@keyframes celebration-bounce {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* Animate In */
@keyframes animate-in {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Scroll Animation Classes */
.animate-in {
    animation: animate-in 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-child {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-child.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Hover States */
.cyber-card:hover {
    animation: cyber-pulse 2s ease infinite;
}

.holo-card:hover {
    animation: holographic-shimmer 1s ease infinite;
}

.neon-border-card:hover {
    animation: electric-border 2s linear infinite;
}

.matrix-card:hover {
    animation: matrix-digital-rain 3s linear infinite;
}

/* Button Hover Enhancements */
.btn-cyber:hover {
    animation: cyber-glitch 0.1s ease infinite;
    transform: translateY(-2px) scale(1.05);
}

.btn-neon:hover {
    animation: neon-text-flicker 0.1s ease infinite;
}

.btn-glitch:hover {
    animation: cyber-glitch 0.2s ease infinite;
}

.btn-matrix:hover {
    animation: matrix-digital-rain 2s linear infinite;
}

/* Particle Hover Effects */
.particle:hover {
    animation-play-state: paused;
    transform: scale(2) !important;
    opacity: 1 !important;
    filter: brightness(1.5);
}

/* Loading State Enhancements */
.loading-brain {
    animation:
        cyber-pulse 2s ease infinite,
        float 3s ease-in-out infinite;
}

/* Enhanced Transitions */
.transition-cyber {
    transition: all var(--speed-fast) cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-cyber:hover {
    transform: translateY(-5px) scale(1.02);
    filter: brightness(1.1) saturate(1.2);
}

/* 🌊 THEME-SPECIFIC ANIMATIONS 🌊 */

/* Ocean Theme Animations */
@keyframes ocean-wave {
    0%, 100% {
        transform: translateY(0px) scale(1);
        filter: hue-rotate(0deg);
    }
    50% {
        transform: translateY(-10px) scale(1.1);
        filter: hue-rotate(30deg);
    }
}

/* Sunset Theme Animations */
@keyframes sunset-glow {
    0%, 100% {
        box-shadow: 0 0 10px var(--primary-1);
        filter: brightness(1);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-2), 0 0 30px var(--primary-3);
        filter: brightness(1.3);
    }
}

/* Forest Theme Animations */
@keyframes forest-sway {
    0%, 100% {
        transform: translateX(0px) rotate(0deg);
    }
    25% {
        transform: translateX(5px) rotate(2deg);
    }
    75% {
        transform: translateX(-5px) rotate(-2deg);
    }
}

/* Royal Theme Animations */
@keyframes royal-sparkle {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
        box-shadow: 0 0 15px var(--primary-1);
    }
}

/* Aurora Theme Animations */
@keyframes aurora-dance {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        filter: hue-rotate(0deg);
    }
    25% {
        transform: translateY(-15px) translateX(10px);
        filter: hue-rotate(90deg);
    }
    50% {
        transform: translateY(-5px) translateX(-5px);
        filter: hue-rotate(180deg);
    }
    75% {
        transform: translateY(-20px) translateX(15px);
        filter: hue-rotate(270deg);
    }
}

/* Cosmic Theme Animations */
@keyframes cosmic-drift {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.8;
    }
    33% {
        transform: translateY(-30px) scale(1.3);
        opacity: 1;
    }
    66% {
        transform: translateY(-10px) scale(0.7);
        opacity: 0.6;
    }
}

/* Theme-specific Button Enhancements */
:root[data-theme="ocean"] .btn-cyber {
    border-color: var(--primary-1);
    box-shadow: 0 0 20px var(--primary-1);
}

:root[data-theme="sunset"] .btn-neon {
    border-color: var(--primary-2);
    color: var(--primary-2);
}

:root[data-theme="forest"] .btn-matrix {
    border-color: var(--accent-1);
    color: var(--accent-1);
}

:root[data-theme="royal"] .btn-holographic {
    background: var(--gradient-primary);
}

:root[data-theme="aurora"] .btn-cyber {
    background: var(--gradient-secondary);
}

:root[data-theme="cosmic"] .btn-neon {
    border-color: var(--accent-1);
    color: var(--accent-1);
}

/* Theme Transition Effects */
.theme-transition {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Card Themes */
:root[data-theme="ocean"] .cyber-card {
    background: linear-gradient(135deg,
        rgba(0, 119, 190, 0.1) 0%,
        rgba(0, 168, 204, 0.1) 50%,
        rgba(64, 224, 208, 0.1) 100%);
}

:root[data-theme="sunset"] .holo-card {
    background: linear-gradient(135deg,
        rgba(255, 107, 53, 0.1) 0%,
        rgba(247, 147, 30, 0.1) 50%,
        rgba(255, 190, 11, 0.1) 100%);
}

:root[data-theme="forest"] .neon-border-card {
    border-color: var(--primary-2);
    background: rgba(45, 80, 22, 0.3);
}

:root[data-theme="royal"] .matrix-card {
    background: linear-gradient(135deg,
        rgba(76, 29, 149, 0.3) 0%,
        rgba(124, 58, 237, 0.2) 100%);
    border-color: var(--primary-2);
}

:root[data-theme="aurora"] .cyber-card {
    background: linear-gradient(135deg,
        rgba(6, 95, 70, 0.2) 0%,
        rgba(5, 150, 105, 0.1) 50%,
        rgba(52, 211, 153, 0.1) 100%);
}

:root[data-theme="cosmic"] .holo-card {
    background: linear-gradient(135deg,
        rgba(26, 26, 46, 0.8) 0%,
        rgba(83, 52, 131, 0.3) 50%,
        rgba(247, 37, 133, 0.1) 100%);
}

/* 🔐 AUTHENTICATION MODAL STYLES 🔐 */

.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-modal.show {
    opacity: 1;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    background: var(--gradient-primary);
    border-radius: 20px;
    padding: 40px;
    max-width: 450px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 50px var(--primary-1);
    border: 2px solid var(--primary-2);
    animation: modal-slide-in 0.3s ease-out;
}

@keyframes modal-slide-in {
    0% {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--primary-2);
}

.modal-title {
    color: white;
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 0 20px var(--primary-1);
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 30px;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-group input {
    padding: 15px 20px;
    border: 2px solid var(--primary-2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-1);
    box-shadow: 0 0 20px var(--primary-1);
    background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.auth-form .btn-cyber {
    margin-top: 20px;
    padding: 18px;
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.auth-switch {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 20px;
    font-size: 14px;
}

.auth-switch a {
    color: var(--primary-1);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-switch a:hover {
    color: var(--primary-2);
    text-shadow: 0 0 10px var(--primary-1);
}

/* User Menu Styles */
.auth-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
    position: relative;
    z-index: 1000;
}

.auth-buttons button {
    position: relative;
    z-index: 1001;
    pointer-events: auto !important;
    cursor: pointer !important;
}

.theme-selector {
    position: relative;
    z-index: 1000;
}

.theme-toggle-btn {
    position: relative;
    z-index: 1001;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 🚨 DEBUG STYLES FOR BUTTONS 🚨 */
#loginBtn, #registerBtn, #themeToggle {
    position: relative !important;
    z-index: 9999 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    border: 2px solid red !important; /* Temporary debug border */
    background: rgba(255, 0, 0, 0.3) !important; /* Temporary debug background */
}

#loginBtn:hover, #registerBtn:hover, #themeToggle:hover {
    background: rgba(255, 0, 0, 0.5) !important;
    transform: scale(1.05) !important;
}

.user-menu {
    display: none;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid var(--primary-2);
}

.user-name {
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 14px;
}

/* File Management Styles */
.file-history {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid var(--primary-2);
}

.file-history h3 {
    color: white;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-history h3::before {
    content: "📁";
    font-size: 24px;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--primary-3);
    transition: all 0.3s ease;
}

.file-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.file-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.file-name {
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.file-date,
.file-size {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.file-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff3742, #ff2f3a);
    transform: translateY(-2px);
}

.no-files {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    padding: 40px;
}

/* Advanced Options */
.advanced-options {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid var(--primary-2);
}

.advanced-options h3 {
    color: white;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.advanced-options h3::before {
    content: "⚙️";
    font-size: 24px;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.option-group:hover {
    background: rgba(255, 255, 255, 0.1);
}

.option-group.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.option-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-1);
}

.option-group label {
    color: white;
    font-weight: 600;
    cursor: pointer;
    flex: 1;
}

.option-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    margin-top: 5px;
}

/* Premium Badge */
.premium-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        padding: 30px 20px;
        margin: 20px;
    }

    .auth-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .file-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Shooting Stars */
.shooting-star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--neon-cyan);
    border-radius: 50%;
    box-shadow:
        0 0 10px var(--neon-cyan),
        0 0 20px var(--neon-cyan),
        0 0 30px var(--neon-cyan);
    animation: shooting-star 3s linear infinite;
}

@keyframes shooting-star {
    0% {
        transform: translateX(-100vw) translateY(100vh) rotate(45deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(100vw) translateY(-100vh) rotate(45deg);
        opacity: 0;
    }
}

/* Energy Orbs */
.energy-orb {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--neon-cyan), transparent);
    box-shadow:
        0 0 20px var(--neon-cyan),
        0 0 40px var(--neon-cyan),
        inset 0 0 10px rgba(0, 255, 255, 0.5);
    animation: energy-orb-float 8s ease-in-out infinite;
}

@keyframes energy-orb-float {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-100px) scale(1.5);
        opacity: 1;
    }
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 12s; }
.particle:nth-child(2) { left: 20%; animation-delay: 2s; animation-duration: 15s; }
.particle:nth-child(3) { left: 30%; animation-delay: 4s; animation-duration: 18s; }
.particle:nth-child(4) { left: 40%; animation-delay: 6s; animation-duration: 14s; }
.particle:nth-child(5) { left: 50%; animation-delay: 8s; animation-duration: 16s; }
.particle:nth-child(6) { left: 60%; animation-delay: 10s; animation-duration: 13s; }
.particle:nth-child(7) { left: 70%; animation-delay: 12s; animation-duration: 17s; }
.particle:nth-child(8) { left: 80%; animation-delay: 14s; animation-duration: 19s; }

/* 🌟 SPECTACULAR CARD & GLASS EFFECTS 🌟 */

/* Enhanced Glass Morphism */
.glass-morphism {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(25px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(0, 255, 255, 0.1);
    transition: all var(--speed-medium) ease;
}

.glass-morphism:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        0 0 50px rgba(0, 255, 255, 0.2);
    transform: translateY(-5px);
}

/* Cyber Card Design */
.cyber-card {
    position: relative;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1) 0%,
        rgba(255, 0, 255, 0.1) 50%,
        rgba(255, 255, 0, 0.1) 100%);
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    border-radius: 20px;
    padding: 30px;
    overflow: hidden;
    transition: all var(--speed-medium) ease;
    clip-path: polygon(0 0, calc(100% - 30px) 0, 100% 30px, 100% 100%, 30px 100%, 0 calc(100% - 30px));
}

.cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-cyber);
    opacity: 0;
    transition: opacity var(--speed-medium) ease;
    z-index: -1;
}

.cyber-card:hover::before {
    opacity: 0.1;
}

.cyber-card:hover {
    border-color: var(--neon-cyan);
    box-shadow:
        0 0 40px var(--neon-cyan),
        inset 0 0 40px rgba(0, 255, 255, 0.1);
    transform: translateY(-10px) rotateX(5deg);
}

/* Holographic Card */
.holo-card {
    position: relative;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 35px;
    overflow: hidden;
    transition: all var(--speed-slow) ease;
}

.holo-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        var(--neon-cyan),
        transparent,
        var(--neon-magenta),
        transparent,
        var(--neon-yellow),
        transparent
    );
    animation: holographic-shimmer 4s linear infinite;
    opacity: 0;
    transition: opacity var(--speed-medium) ease;
    z-index: -1;
}

.holo-card:hover::before {
    opacity: 0.1;
}

.holo-card:hover {
    transform: translateY(-8px) rotateY(5deg) rotateX(5deg);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 50px rgba(255, 255, 255, 0.2);
}

/* Neon Border Card */
.neon-border-card {
    position: relative;
    background: rgba(0, 0, 0, 0.8);
    border: 3px solid var(--neon-magenta);
    border-radius: 15px;
    padding: 25px;
    overflow: hidden;
    animation: electric-border 6s linear infinite;
    transition: all var(--speed-medium) ease;
}

.neon-border-card:hover {
    background: rgba(255, 0, 255, 0.1);
    box-shadow:
        0 0 30px var(--neon-magenta),
        0 0 60px var(--neon-magenta),
        inset 0 0 30px rgba(255, 0, 255, 0.1);
    transform: scale(1.02);
}

/* Matrix Style Card */
.matrix-card {
    position: relative;
    background: linear-gradient(135deg,
        rgba(0, 51, 0, 0.9) 0%,
        rgba(0, 102, 0, 0.7) 100%);
    border: 2px solid var(--neon-green);
    border-radius: 10px;
    padding: 20px;
    overflow: hidden;
    font-family: 'JetBrains Mono', monospace;
    transition: all var(--speed-medium) ease;
}

.matrix-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 0, 0.1) 2px,
        rgba(0, 255, 0, 0.1) 4px
    );
    pointer-events: none;
    animation: matrix-digital-rain 10s linear infinite;
}

.matrix-card:hover {
    background: linear-gradient(135deg,
        rgba(0, 102, 0, 0.9) 0%,
        rgba(0, 153, 0, 0.8) 100%);
    box-shadow:
        0 0 40px var(--neon-green),
        inset 0 0 20px rgba(0, 255, 0, 0.2);
    transform: translateY(-5px);
}

/* Neon glow effects */
.neon-blue {
    color: #3b82f6;
    text-shadow:
        0 0 5px #3b82f6,
        0 0 10px #3b82f6,
        0 0 15px #3b82f6,
        0 0 20px #3b82f6;
    animation: neon-flicker 3s ease-in-out infinite;
}

.neon-purple {
    color: #8b5cf6;
    text-shadow:
        0 0 5px #8b5cf6,
        0 0 10px #8b5cf6,
        0 0 15px #8b5cf6,
        0 0 20px #8b5cf6;
    animation: neon-flicker 3s ease-in-out infinite;
    animation-delay: 1s;
}

.neon-pink {
    color: #ec4899;
    text-shadow:
        0 0 5px #ec4899,
        0 0 10px #ec4899,
        0 0 15px #ec4899,
        0 0 20px #ec4899;
    animation: neon-flicker 3s ease-in-out infinite;
    animation-delay: 2s;
}

/* Holographic text effect */
.holographic {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b, #10b981);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: hologram 4s ease-in-out infinite;
}

/* 🎮 SPECTACULAR BUTTON DESIGNS 🎮 */

/* Enhanced Cyber Button */
.btn-cyber {
    position: relative;
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradient-shift 3s ease infinite;
    border: 2px solid var(--primary-1);
    border-radius: 0;
    padding: 16px 32px;
    color: white;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    overflow: hidden;
    cursor: pointer;
    transform: perspective(1000px) rotateX(0deg);
    transition: all var(--speed-fast) cubic-bezier(0.4, 0, 0.2, 1);
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
    box-shadow:
        0 0 20px var(--primary-1),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

/* Enhanced Neon Button */
.btn-neon {
    position: relative;
    background: transparent;
    border: 3px solid var(--primary-2);
    border-radius: 15px;
    padding: 18px 36px;
    color: var(--primary-2);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 3px;
    cursor: pointer;
    overflow: hidden;
    transition: all var(--speed-medium) ease;
    animation: electric-border 4s linear infinite;
}

.btn-neon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--neon-magenta), transparent);
    transition: left var(--speed-medium) ease;
    opacity: 0.3;
}

.btn-neon:hover::before {
    left: 100%;
}

.btn-neon:hover {
    color: white;
    background: var(--neon-magenta);
    box-shadow:
        0 0 30px var(--neon-magenta),
        0 0 60px var(--neon-magenta),
        inset 0 0 30px rgba(255, 0, 255, 0.2);
    transform: translateY(-5px) scale(1.05);
    animation: cyber-pulse 1s ease infinite;
}

/* Holographic Button Enhanced */
.btn-holographic {
    position: relative;
    background: var(--gradient-rainbow);
    background-size: 400% 400%;
    animation: holographic-shimmer 3s ease infinite;
    border: none;
    border-radius: 20px;
    padding: 20px 40px;
    color: white;
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    overflow: hidden;
    cursor: pointer;
    transform: perspective(1000px) rotateX(0deg);
    transition: all var(--speed-fast) cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 255, 255, 0.2);
}

/* Glitch Button */
.btn-glitch {
    position: relative;
    background: linear-gradient(45deg, var(--electric-blue), var(--electric-purple));
    border: 2px solid var(--neon-cyan);
    border-radius: 8px;
    padding: 16px 32px;
    color: white;
    font-family: 'Fira Code', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    overflow: hidden;
    transition: all var(--speed-fast) ease;
}

.btn-glitch:hover {
    animation: cyber-glitch 0.3s ease infinite;
    box-shadow:
        0 0 20px var(--neon-cyan),
        0 0 40px var(--electric-purple);
}

/* Matrix Button */
.btn-matrix {
    position: relative;
    background: var(--gradient-matrix);
    border: 2px solid var(--neon-green);
    border-radius: 0;
    padding: 14px 28px;
    color: var(--neon-green);
    font-family: 'JetBrains Mono', monospace;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
    cursor: pointer;
    overflow: hidden;
    transition: all var(--speed-fast) ease;
    clip-path: polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0 100%);
}

.btn-matrix::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        var(--neon-green) 2px,
        var(--neon-green) 4px
    );
    opacity: 0;
    transition: opacity var(--speed-fast) ease;
}

.btn-matrix:hover::after {
    opacity: 0.1;
}

.btn-matrix:hover {
    background: var(--neon-green);
    color: black;
    box-shadow:
        0 0 30px var(--neon-green),
        inset 0 0 20px rgba(0, 255, 0, 0.2);
    animation: neon-text-flicker 0.1s ease infinite;
}

.btn-holographic::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-holographic:hover::before {
    left: 100%;
}

.btn-holographic:hover {
    transform: perspective(1000px) rotateX(10deg) translateY(-5px);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.3),
        0 0 30px rgba(59, 130, 246, 0.5),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

.btn-holographic:active {
    transform: perspective(1000px) rotateX(5deg) translateY(-2px);
}

/* Cyberpunk grid overlay */
.cyber-grid {
    position: relative;
    overflow: hidden;
}

.cyber-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: matrix-rain 30s linear infinite;
    pointer-events: none;
}

/* Enhanced drop zone with cyberpunk styling */
#dropZone {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 2px dashed rgba(59, 130, 246, 0.3);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#dropZone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

#dropZone:hover {
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.2),
        0 0 30px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255,255,255,0.1);
}

#dropZone:hover::before {
    left: 100%;
}

#dropZone.dragover {
    border-color: rgba(139, 92, 246, 0.8);
    background: rgba(139, 92, 246, 0.2);
    transform: scale(1.02);
    box-shadow:
        0 25px 50px rgba(0,0,0,0.3),
        0 0 50px rgba(139, 92, 246, 0.5),
        inset 0 1px 0 rgba(255,255,255,0.2);
}

/* Futuristic form inputs */
textarea, input[type="text"] {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 12px !important;
    color: white !important;
    padding: 16px !important;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

textarea:focus, input[type="text"]:focus {
    outline: none !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    background: rgba(59, 130, 246, 0.1) !important;
    box-shadow:
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px);
}

textarea::placeholder, input::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Animated progress bars */
.progress-bar-fill {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    background-size: 200% 100%;
    animation: gradient-shift 2s ease infinite;
    position: relative;
    overflow: hidden;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 1.5s ease infinite;
}

/* Step indicators with advanced styling */
.step-number, .step-check {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#step1.completed .step-number,
#step2.completed .step-number,
#step3.completed .step-number {
    opacity: 0;
    transform: scale(0);
}

#step1.completed .step-check,
#step2.completed .step-check,
#step3.completed .step-check {
    opacity: 1;
    transform: scale(1);
    animation: pulse-glow 2s ease infinite;
}

/* Enhanced list item styles */
.strength-item {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    backdrop-filter: blur(10px);
}

.strength-item:hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.2) 100%);
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15);
}

.missing-skill-item {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%);
    border: 1px solid rgba(249, 115, 22, 0.2);
    backdrop-filter: blur(10px);
}

.missing-skill-item:hover {
    background: linear-gradient(135deg, rgba(249, 115, 22, 0.2) 0%, rgba(239, 68, 68, 0.2) 100%);
    border-color: rgba(249, 115, 22, 0.4);
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
}

.recommendation-item {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
}

.recommendation-item:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Celebration animation */
@keyframes celebration {
    0% { transform: scale(0) rotate(0deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
    100% { transform: scale(1) rotate(360deg); opacity: 1; }
}

.celebration {
    animation: celebration 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Confetti animation */
@keyframes confetti-fall {
    0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
    animation: confetti-fall 3s linear infinite;
    z-index: 1000;
}

/* Enhanced footer */
footer {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .particles {
        display: none; /* Hide particles on mobile for performance */
    }

    .bg-grid-pattern {
        opacity: 0.05; /* Reduce grid opacity on mobile */
    }

    /* Simplify animations on mobile */
    .animate-float,
    .animate-shimmer,
    .animate-glow {
        animation: none;
    }

    /* Reduce blur effects on mobile */
    .backdrop-blur-2xl {
        backdrop-filter: blur(10px);
    }

    .backdrop-blur-xl {
        backdrop-filter: blur(8px);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-white\/10 {
        background: rgba(255, 255, 255, 0.2);
    }

    .border-white\/20 {
        border-color: rgba(255, 255, 255, 0.4);
    }

    .text-blue-200 {
        color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .animate-spin,
    .animate-pulse,
    .animate-bounce,
    .animate-float,
    .animate-shimmer,
    .animate-glow {
        animation: none;
    }
}

/* Print styles */
@media print {
    .particles,
    .bg-grid-pattern,
    .backdrop-blur-2xl,
    .backdrop-blur-xl {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .bg-gradient-to-r,
    .bg-gradient-to-br {
        background: white;
    }

    .text-white {
        color: black;
    }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
}

/* Selection styling */
::selection {
    background: rgba(59, 130, 246, 0.3);
    color: white;
}

::-moz-selection {
    background: rgba(59, 130, 246, 0.3);
    color: white;
}

/* Enhanced tab styles with cyberpunk theme */
.upload-tab {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px 8px 0 0;
    padding: 12px 24px;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.upload-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    transition: left 0.5s;
}

.upload-tab:hover::before {
    left: 100%;
}

.upload-tab.active {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.5);
    color: #60a5fa !important;
    box-shadow:
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.upload-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    animation: shimmer 2s ease infinite;
}

.upload-tab:hover {
    border-bottom-color: #9ca3af !important;
    color: #374151 !important;
}

/* Upload content transitions */
.upload-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced drop zone */
#dropZone {
    position: relative;
    overflow: hidden;
}

#dropZone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

#dropZone:hover::before {
    left: 100%;
}

/* Progress steps animation */
.progress-step {
    transition: all 0.3s ease-in-out;
}

.progress-step.completed .step-circle {
    background-color: #3b82f6;
    color: white;
    transform: scale(1.1);
}

.progress-step.completed .step-text {
    color: #3b82f6;
    font-weight: 600;
}

/* Button enhancements */
button {
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

button:active::before {
    width: 300px;
    height: 300px;
}

/* Gradient button */
.gradient-button {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Toast notifications */
.toast {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Enhanced loading animation */
.loading-brain {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Character counter styling */
.character-counter {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-indicator {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Drop zone hover effects */
#dropZone.dragover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* File upload animations */
#fileInfo {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation */
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Results animation */
#resultsSection {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Match score circle animation */
#matchScoreCircle {
    transition: stroke-dasharray 1s ease-in-out;
}

/* Custom button hover effects */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active {
    transform: translateY(0);
}

/* Form input focus effects */
input:focus,
textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* List item animations */
.list-item {
    animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Strength items */
.strength-item {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-left: 4px solid #10b981;
}

/* Missing skill items */
.missing-skill-item {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left: 4px solid #ef4444;
}

/* Recommendation items */
.recommendation-item {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-left: 4px solid #3b82f6;
}

/* Status indicators */
.status-met {
    color: #10b981;
}

.status-not-met {
    color: #ef4444;
}

.status-partial {
    color: #f59e0b;
}

/* Card hover effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Progress bar styles */
.progress-bar {
    background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
    
    h1 {
        font-size: 1.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .text-4xl {
        font-size: 2.5rem;
    }
}

/* Print styles */
@media print {
    header,
    footer,
    #analysisForm,
    #newAnalysisBtn {
        display: none;
    }
    
    #resultsSection {
        display: block !important;
    }
    
    .shadow-lg {
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    /* Add dark mode styles if needed */
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators for keyboard navigation */
button:focus,
input:focus,
textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .border-gray-300 {
        border-color: #000;
    }
    
    .text-gray-600 {
        color: #000;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test</title>
    <style>
        body {
            background: #000;
            color: white;
            font-family: Arial, sans-serif;
            padding: 50px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            position: relative;
            z-index: 1000;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <h1>Button Test Page</h1>
    <p>If these buttons work, then there's an issue with the main page CSS/JS.</p>
    
    <button class="test-button" onclick="alert('Button 1 works!')">Test Button 1</button>
    <button class="test-button" onclick="alert('Button 2 works!')">Test Button 2</button>
    <button class="test-button" onclick="alert('Button 3 works!')">Test Button 3</button>
    
    <br><br>
    
    <button class="test-button" onclick="console.log('Console test works'); alert('Console + Alert works!')">Console Test</button>
    
    <script>
        console.log('Test page loaded successfully');
        
        // Test if JavaScript is working
        setTimeout(() => {
            console.log('JavaScript is working on test page');
        }, 1000);
    </script>
</body>
</html>

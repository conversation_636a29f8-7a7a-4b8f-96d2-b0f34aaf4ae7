<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume-to-Job Match Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">Resume Match Analyzer</h1>
                </div>
                <div class="text-sm text-gray-500">
                    <i class="fas fa-aws text-orange-500 mr-1"></i>
                    AWS Lambda Hackathon Entry
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                Discover How Well Your Resume Matches Any Job
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Upload your resume and paste a job description to get AI-powered insights on your match score, 
                missing skills, and personalized recommendations for improvement.
            </p>
        </div>

        <!-- Analysis Form -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <form id="analysisForm" class="space-y-8">
                <!-- Resume Upload Section -->
                <div>
                    <label class="block text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-file-upload mr-2 text-blue-600"></i>
                        Upload Your Resume
                    </label>
                    
                    <!-- File Upload Options -->
                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- File Upload -->
                        <div>
                            <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-lg text-gray-600 mb-2">Drop your resume here or click to browse</p>
                                <p class="text-sm text-gray-500">Supports PDF, DOCX, and TXT files (max 5MB)</p>
                                <input type="file" id="resumeFile" accept=".pdf,.docx,.txt" class="hidden">
                            </div>
                            <div id="fileInfo" class="mt-4 hidden">
                                <div class="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                                    <i class="fas fa-file-check text-green-600 mr-3"></i>
                                    <span id="fileName" class="text-green-800"></span>
                                    <button type="button" id="removeFile" class="ml-auto text-red-600 hover:text-red-800">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Text Input Alternative -->
                        <div>
                            <label for="resumeText" class="block text-sm font-medium text-gray-700 mb-2">
                                Or paste your resume text:
                            </label>
                            <textarea 
                                id="resumeText" 
                                rows="10" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Paste your resume content here..."
                            ></textarea>
                        </div>
                    </div>
                </div>

                <!-- Job Description Section -->
                <div>
                    <label for="jobDescription" class="block text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-briefcase mr-2 text-blue-600"></i>
                        Job Description
                    </label>
                    <textarea 
                        id="jobDescription" 
                        rows="12" 
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        placeholder="Paste the complete job description here including requirements, responsibilities, and qualifications..."
                    ></textarea>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button 
                        type="submit" 
                        id="analyzeBtn"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <i class="fas fa-chart-line mr-2"></i>
                        Analyze Match
                    </button>
                </div>
            </form>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="hidden bg-white rounded-lg shadow-lg p-8 text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Analyzing Your Resume...</h3>
            <p class="text-gray-600">Our AI is comparing your resume with the job requirements. This may take a few moments.</p>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <!-- Match Score Card -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Match Analysis Results</h3>
                    <div class="relative inline-block">
                        <div class="w-32 h-32 mx-auto">
                            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                                <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                <path id="matchScoreCircle" class="text-blue-600" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="0, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            </svg>
                        </div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span id="matchPercentage" class="text-3xl font-bold text-gray-900">0%</span>
                        </div>
                    </div>
                </div>
                <p id="overallAssessment" class="text-lg text-gray-700 text-center max-w-3xl mx-auto"></p>
            </div>

            <!-- Detailed Analysis -->
            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <!-- Strengths -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-green-700 mb-4">
                        <i class="fas fa-check-circle mr-2"></i>
                        Your Strengths
                    </h4>
                    <ul id="strengthsList" class="space-y-3"></ul>
                </div>

                <!-- Missing Skills -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-red-700 mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Areas for Improvement
                    </h4>
                    <ul id="missingSkillsList" class="space-y-3"></ul>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h4 class="text-xl font-bold text-blue-700 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Personalized Recommendations
                </h4>
                <ul id="recommendationsList" class="space-y-3"></ul>
            </div>

            <!-- Experience & Education Analysis -->
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Experience Analysis -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-briefcase mr-2"></i>
                        Experience Analysis
                    </h4>
                    <div id="experienceAnalysis" class="space-y-3"></div>
                </div>

                <!-- Education Analysis -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Education Analysis
                    </h4>
                    <div id="educationAnalysis" class="space-y-3"></div>
                </div>
            </div>

            <!-- New Analysis Button -->
            <div class="text-center mt-8">
                <button 
                    id="newAnalysisBtn"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                >
                    <i class="fas fa-plus mr-2"></i>
                    Analyze Another Resume
                </button>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden bg-red-50 border border-red-200 rounded-lg p-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle text-red-600 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">Analysis Failed</h3>
                    <p id="errorMessage" class="text-red-700 mt-1"></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-300">
                Built with <i class="fas fa-heart text-red-500"></i> for the AWS Lambda Hackathon 2024
            </p>
            <p class="text-gray-400 text-sm mt-2">
                Powered by AWS Lambda, Amazon Bedrock (Claude), and modern web technologies
            </p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

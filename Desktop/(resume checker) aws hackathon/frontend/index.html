<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume-to-Job Match Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace'],
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'shimmer': 'shimmer 2s linear infinite',
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>

    <!-- 🚨 EMERGENCY BUTTON FIX 🚨 -->
    <script>
        console.log('🚨 Emergency script loaded in head');

        // Simple function that definitely works
        function testClick(buttonName) {
            console.log(`🔥 ${buttonName} button clicked!`);
            alert(`${buttonName} button works!`);
        }

        // Ensure functions are available globally
        window.testClick = testClick;

        // Add event listeners as soon as DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 Emergency DOM ready handler');

            setTimeout(() => {
                const loginBtn = document.getElementById('loginBtn');
                const registerBtn = document.getElementById('registerBtn');
                const themeBtn = document.getElementById('themeToggle');

                console.log('🔍 Emergency check:', {
                    loginBtn: !!loginBtn,
                    registerBtn: !!registerBtn,
                    themeBtn: !!themeBtn
                });

                if (loginBtn) {
                    loginBtn.style.border = '3px solid lime';
                    loginBtn.style.background = 'rgba(0, 255, 0, 0.3)';
                }
                if (registerBtn) {
                    registerBtn.style.border = '3px solid lime';
                    registerBtn.style.background = 'rgba(0, 255, 0, 0.3)';
                }
                if (themeBtn) {
                    themeBtn.style.border = '3px solid lime';
                    themeBtn.style.background = 'rgba(0, 255, 0, 0.3)';
                }
            }, 1000);
        });
    </script>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen relative overflow-x-hidden">
    <!-- Animated Background -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -inset-10 opacity-50">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 4s;"></div>
            <div class="absolute bottom-0 right-20 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 1s;"></div>
        </div>

        <!-- 🌌 SPECTACULAR PARTICLE SYSTEM 🌌 -->
        <div class="particles">
            <!-- Cyber Particles -->
            <div class="particle cyber"></div>
            <div class="particle cyber"></div>
            <div class="particle cyber"></div>

            <!-- Neon Particles -->
            <div class="particle neon"></div>
            <div class="particle neon"></div>
            <div class="particle neon"></div>

            <!-- Electric Particles -->
            <div class="particle electric"></div>
            <div class="particle electric"></div>

            <!-- Matrix Particles -->
            <div class="particle matrix"></div>
            <div class="particle matrix"></div>
            <div class="particle matrix"></div>
            <div class="particle matrix"></div>

            <!-- Holographic Particles -->
            <div class="particle holo"></div>
            <div class="particle holo"></div>

            <!-- Theme-specific Particles -->
            <div class="particle theme-1"></div>
            <div class="particle theme-1"></div>
            <div class="particle theme-2"></div>
            <div class="particle theme-2"></div>
            <div class="particle accent"></div>
            <div class="particle accent"></div>

            <!-- Shooting Stars -->
            <div class="shooting-star" style="top: 20%; animation-delay: 0s;"></div>
            <div class="shooting-star" style="top: 40%; animation-delay: 1s;"></div>
            <div class="shooting-star" style="top: 60%; animation-delay: 2s;"></div>
            <div class="shooting-star" style="top: 80%; animation-delay: 3s;"></div>

            <!-- Energy Orbs -->
            <div class="energy-orb" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
            <div class="energy-orb" style="top: 30%; right: 15%; animation-delay: 2s;"></div>
            <div class="energy-orb" style="bottom: 20%; left: 20%; animation-delay: 4s;"></div>
            <div class="energy-orb" style="top: 60%; right: 30%; animation-delay: 6s;"></div>
        </div>

        <!-- Grid pattern -->
        <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
    </div>
    <!-- Header -->
    <header class="relative z-10 backdrop-blur-xl bg-white/10 border-b border-white/20 shadow-2xl">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center group">
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
                        <div class="relative bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-lg">
                            <i class="fas fa-brain text-white text-xl animate-pulse"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                            Resume Match Analyzer
                        </h1>
                        <div class="text-xs text-blue-200 font-mono">AI-Powered Career Intelligence</div>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Authentication Buttons -->
                    <div class="auth-buttons flex items-center space-x-3">
                        <button id="loginBtn" class="btn-neon px-4 py-2 text-sm" onclick="alert('Login clicked!'); console.log('Login button works!');">
                            <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                        </button>
                        <button id="registerBtn" class="btn-cyber px-4 py-2 text-sm" onclick="alert('Register clicked!'); console.log('Register button works!');">
                            <i class="fas fa-user-plus mr-2"></i>Sign Up
                        </button>
                    </div>

                    <!-- User Menu (Hidden by default) -->
                    <div class="user-menu" style="display: none;">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="user-name">User</span>
                        <button id="logoutBtn" class="btn-small btn-danger">
                            <i class="fas fa-sign-out-alt mr-1"></i>Logout
                        </button>
                    </div>

                    <!-- Theme Selector -->
                    <div class="theme-selector relative">
                        <button id="themeToggle" class="theme-toggle-btn flex items-center px-3 py-2 bg-white/10 backdrop-blur-xl border border-white/20 rounded-lg hover:bg-white/15 transition-all duration-300" onclick="alert('Theme clicked!'); console.log('Theme button works!');">
                            <i class="fas fa-palette text-white mr-2"></i>
                            <span class="text-white text-sm">Themes</span>
                            <i class="fas fa-chevron-down text-white ml-2 text-xs"></i>
                        </button>
                        <div id="themeDropdown" class="theme-dropdown absolute top-full right-0 mt-2 w-48 bg-black/90 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl hidden z-50">
                            <div class="p-2 space-y-1">
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="cyberpunk">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #00ffff;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #ff00ff;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #ffff00;"></div>
                                    </div>
                                    <span class="text-white text-sm">Cyberpunk</span>
                                </div>
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="ocean">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #0077be;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #00a8cc;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #40e0d0;"></div>
                                    </div>
                                    <span class="text-white text-sm">Ocean Depths</span>
                                </div>
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="sunset">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #ff6b35;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #f7931e;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #ffbe0b;"></div>
                                    </div>
                                    <span class="text-white text-sm">Sunset Vibes</span>
                                </div>
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="forest">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #2d5016;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #52b788;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #95d5b2;"></div>
                                    </div>
                                    <span class="text-white text-sm">Forest Mystique</span>
                                </div>
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="royal">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #4c1d95;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #7c3aed;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #a855f7;"></div>
                                    </div>
                                    <span class="text-white text-sm">Royal Purple</span>
                                </div>
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="aurora">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #065f46;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #059669;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #34d399;"></div>
                                    </div>
                                    <span class="text-white text-sm">Aurora Borealis</span>
                                </div>
                                <div class="theme-option flex items-center px-3 py-2 rounded-lg hover:bg-white/10 cursor-pointer transition-all duration-200" data-theme="cosmic">
                                    <div class="theme-preview flex space-x-1 mr-3">
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #1a1a2e;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #533483;"></div>
                                        <div class="color-dot w-3 h-3 rounded-full" style="background: #f72585;"></div>
                                    </div>
                                    <span class="text-white text-sm">Cosmic Space</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="hidden sm:flex items-center px-3 py-1 bg-gradient-to-r from-orange-500/20 to-yellow-500/20 rounded-full border border-orange-400/30">
                        <i class="fab fa-aws text-orange-400 mr-2 animate-pulse"></i>
                        <span class="text-orange-200 text-sm font-medium">AWS Lambda Hackathon</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-green-200 text-xs">Live</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header glow effect -->
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-pink-600/20 opacity-50"></div>
    </header>

    <!-- Main Content -->
    <main class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-16 relative">
            <!-- Floating elements -->
            <div class="absolute -top-10 left-10 w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 animate-float"></div>
            <div class="absolute -top-5 right-20 w-16 h-16 bg-gradient-to-r from-pink-400 to-red-500 rounded-full opacity-20 animate-float" style="animation-delay: 1s;"></div>

            <div class="mb-8 relative">
                <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-xl border border-blue-400/30 rounded-full text-blue-200 text-sm font-medium mb-6 shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 group">
                    <div class="relative">
                        <i class="fas fa-robot mr-3 text-lg group-hover:animate-bounce"></i>
                        <div class="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur opacity-30 group-hover:opacity-60 transition duration-300"></div>
                    </div>
                    <span class="relative">AI-Powered Resume Analysis</span>
                    <div class="ml-2 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
            </div>

            <div class="relative">
                <h2 class="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                    <span class="bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent animate-glow">
                        Discover How Well Your
                    </span>
                    <br>
                    <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-shimmer">
                        Resume Matches Any Job
                    </span>
                </h2>

                <!-- Decorative elements -->
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50"></div>
            </div>

            <p class="text-xl text-blue-100/80 max-w-3xl mx-auto mb-12 leading-relaxed">
                Upload your resume and paste a job description to get
                <span class="text-blue-300 font-semibold">AI-powered insights</span> on your match score,
                missing skills, and <span class="text-purple-300 font-semibold">personalized recommendations</span> for improvement.
            </p>

            <!-- Enhanced Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-12">
                <div class="cyber-card">
                    <div class="text-4xl mb-3 animate-bounce-slow">⚡</div>
                    <div class="text-lg font-semibold text-white mb-2">Instant Analysis</div>
                    <div class="text-sm text-blue-200">Get results in seconds with our lightning-fast AI</div>
                </div>

                <div class="holo-card">
                    <div class="text-4xl mb-3 animate-pulse-slow">🎯</div>
                    <div class="text-lg font-semibold text-white mb-2">Accurate Matching</div>
                    <div class="text-sm text-green-200">Precision analysis powered by advanced AI models</div>
                </div>

                <div class="neon-border-card">
                    <div class="text-4xl mb-3 animate-bounce-slow" style="animation-delay: 1s;">💡</div>
                    <div class="text-lg font-semibold text-white mb-2">Smart Recommendations</div>
                    <div class="text-sm text-purple-200">Actionable insights to boost your career prospects</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Progress Steps -->
        <div class="mb-12 relative">
            <div class="flex items-center justify-center space-x-8 relative">
                <!-- Step 1 -->
                <div id="step1" class="flex items-center group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur opacity-50 group-hover:opacity-75 transition duration-300"></div>
                    <div class="relative w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-2xl transform group-hover:scale-110 transition-all duration-300">
                        <span class="step-number">1</span>
                        <i class="fas fa-check step-check hidden"></i>
                    </div>
                    <div class="ml-3 hidden sm:block">
                        <div class="text-sm font-semibold text-blue-300">Upload Resume</div>
                        <div class="text-xs text-blue-200/60">PDF, DOCX, or Text</div>
                    </div>
                </div>

                <!-- Connector 1 -->
                <div class="flex-1 max-w-24 relative">
                    <div class="h-1 bg-gradient-to-r from-blue-600/30 to-purple-600/30 rounded-full relative overflow-hidden">
                        <div class="progress-bar-fill h-full bg-gradient-to-r from-blue-600 to-purple-600 rounded-full w-0 transition-all duration-1000"></div>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 animate-pulse"></div>
                </div>

                <!-- Step 2 -->
                <div id="step2" class="flex items-center group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-30 group-hover:opacity-60 transition duration-300"></div>
                    <div class="relative w-12 h-12 bg-white/20 backdrop-blur-xl border border-white/30 text-white/60 rounded-full flex items-center justify-center text-sm font-bold shadow-2xl transform group-hover:scale-110 transition-all duration-300">
                        <span class="step-number">2</span>
                        <i class="fas fa-check step-check hidden"></i>
                    </div>
                    <div class="ml-3 hidden sm:block">
                        <div class="text-sm font-semibold text-white/60">Add Job Description</div>
                        <div class="text-xs text-white/40">Paste job requirements</div>
                    </div>
                </div>

                <!-- Connector 2 -->
                <div class="flex-1 max-w-24 relative">
                    <div class="h-1 bg-gradient-to-r from-purple-600/30 to-pink-600/30 rounded-full relative overflow-hidden">
                        <div class="progress-bar-fill h-full bg-gradient-to-r from-purple-600 to-pink-600 rounded-full w-0 transition-all duration-1000"></div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div id="step3" class="flex items-center group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-pink-600 to-red-600 rounded-full blur opacity-30 group-hover:opacity-60 transition duration-300"></div>
                    <div class="relative w-12 h-12 bg-white/20 backdrop-blur-xl border border-white/30 text-white/60 rounded-full flex items-center justify-center text-sm font-bold shadow-2xl transform group-hover:scale-110 transition-all duration-300">
                        <span class="step-number">3</span>
                        <i class="fas fa-magic step-check hidden"></i>
                    </div>
                    <div class="ml-3 hidden sm:block">
                        <div class="text-sm font-semibold text-white/60">Get Analysis</div>
                        <div class="text-xs text-white/40">AI-powered insights</div>
                    </div>
                </div>
            </div>

            <!-- Mobile step indicators -->
            <div class="sm:hidden flex justify-center mt-4 space-x-2">
                <div class="text-xs text-blue-300 font-medium">1. Upload</div>
                <div class="text-xs text-white/40">•</div>
                <div class="text-xs text-white/60 font-medium">2. Describe</div>
                <div class="text-xs text-white/40">•</div>
                <div class="text-xs text-white/60 font-medium">3. Analyze</div>
            </div>
        </div>

        <!-- File History Section (Premium Feature) -->
        <div class="file-history">
            <h3>Your Uploaded Files</h3>
            <div id="userFilesList">
                <p class="no-files">Sign in to view your file history</p>
            </div>
        </div>

        <!-- Advanced Options (Premium Feature) -->
        <div class="advanced-options">
            <h3>Advanced Analysis Options</h3>
            <div class="option-group">
                <input type="checkbox" id="enableA2IReview" disabled>
                <label for="enableA2IReview">
                    Enable Human Review (A2I)
                    <span class="premium-badge">Premium</span>
                </label>
                <div class="option-description">
                    Get your analysis reviewed by human experts for enhanced accuracy
                </div>
            </div>
            <div class="option-group">
                <input type="checkbox" id="enableGeminiAnalysis" checked>
                <label for="enableGeminiAnalysis">
                    Enhanced AI Analysis (Gemini)
                    <span class="premium-badge">Premium</span>
                </label>
                <div class="option-description">
                    Use Google Gemini for line-by-line detailed analysis
                </div>
            </div>
        </div>

        <!-- Analysis Form -->
        <div class="relative group mb-12">
            <!-- Glowing border effect -->
            <div class="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>

            <div class="relative bg-white/10 backdrop-blur-2xl border border-white/20 rounded-2xl p-8 shadow-2xl">
                <!-- Form header -->
                <div class="text-center mb-8">
                    <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full border border-blue-400/30 mb-4">
                        <i class="fas fa-magic text-blue-300 mr-2"></i>
                        <span class="text-blue-200 text-sm font-medium">AI Analysis Form</span>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-2">Let's Analyze Your Resume</h3>
                    <p class="text-blue-200/80">Follow the steps below to get your personalized career insights</p>
                </div>

                <form id="analysisForm" class="space-y-10">
                <!-- Resume Upload Section -->
                <div id="resumeSection">
                    <div class="flex items-center justify-between mb-4">
                        <label class="block text-lg font-semibold text-gray-900">
                            <i class="fas fa-file-upload mr-2 text-blue-600"></i>
                            Step 1: Upload Your Resume
                        </label>
                        <div id="resumeStatus" class="hidden">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Complete
                            </span>
                        </div>
                    </div>

                    <!-- Upload Methods Tabs -->
                    <div class="mb-6">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button type="button" id="fileTab" class="upload-tab active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                    <i class="fas fa-upload mr-2"></i>Upload File
                                </button>
                                <button type="button" id="textTab" class="upload-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <i class="fas fa-edit mr-2"></i>Paste Text
                                </button>
                                <button type="button" id="sampleTab" class="upload-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <i class="fas fa-eye mr-2"></i>Try Sample
                                </button>
                            </nav>
                        </div>
                    </div>

                    <!-- File Upload Tab -->
                    <div id="fileUploadTab" class="upload-content">
                        <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-all duration-200 cursor-pointer group">
                            <div class="group-hover:scale-105 transition-transform duration-200">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4 group-hover:text-blue-500"></i>
                                <p class="text-lg text-gray-600 mb-2">Drop your resume here or click to browse</p>
                                <p class="text-sm text-gray-500">Supports PDF, DOCX, and TXT files (max 5MB)</p>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        Secure & Private
                                    </span>
                                </div>
                            </div>
                            <input type="file" id="resumeFile" accept=".pdf,.docx,.txt" class="hidden">
                        </div>
                        <div id="fileInfo" class="mt-4 hidden">
                            <div class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg">
                                <i class="fas fa-file-check text-green-600 mr-3 text-xl"></i>
                                <div class="flex-1">
                                    <span id="fileName" class="text-green-800 font-medium"></span>
                                    <div id="fileSize" class="text-green-600 text-sm"></div>
                                </div>
                                <button type="button" id="removeFile" class="ml-auto text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-50">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Text Input Tab -->
                    <div id="textInputTab" class="upload-content hidden">
                        <div class="relative">
                            <textarea
                                id="resumeText"
                                rows="12"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                placeholder="Paste your resume content here...

Example:
John Smith
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE
Senior Developer at TechCorp (2020-Present)
- Developed web applications using React and Node.js
- Led team of 3 developers
..."
                            ></textarea>
                            <div id="textCounter" class="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
                                0 characters
                            </div>
                        </div>
                    </div>

                    <!-- Sample Resume Tab -->
                    <div id="sampleResumeTab" class="upload-content hidden">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                <h3 class="text-lg font-medium text-blue-900">Try with Sample Resume</h3>
                            </div>
                            <p class="text-blue-700 mb-4">
                                Want to see how it works? Use our sample resume to test the analysis feature.
                            </p>
                            <button type="button" id="loadSampleBtn" class="btn-cyber">
                                <i class="fas fa-download mr-2"></i>
                                Load Sample Resume
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Description Section -->
                <div id="jobSection">
                    <div class="flex items-center justify-between mb-4">
                        <label class="block text-lg font-semibold text-gray-900">
                            <i class="fas fa-briefcase mr-2 text-blue-600"></i>
                            Step 2: Add Job Description
                        </label>
                        <div id="jobStatus" class="hidden">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Complete
                            </span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- Job Description Input -->
                        <div class="relative">
                            <textarea
                                id="jobDescription"
                                rows="12"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
                                placeholder="Paste the complete job description here...

Example:
Senior Software Engineer - Full Stack
Company: TechCorp Inc.

Requirements:
- 5+ years of experience in web development
- Proficiency in React, Node.js, and Python
- Experience with AWS cloud services
- Bachelor's degree in Computer Science

Responsibilities:
- Develop and maintain web applications
- Collaborate with cross-functional teams
- Mentor junior developers
..."
                            ></textarea>
                            <div id="jobCounter" class="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
                                0 characters
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex flex-wrap gap-2">
                            <button type="button" id="loadSampleJobBtn" class="btn-neon">
                                <i class="fas fa-file-alt mr-1"></i>
                                Load Sample Job
                            </button>
                            <button type="button" id="clearJobBtn" class="btn-matrix">
                                <i class="fas fa-eraser mr-1"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Analysis Section -->
                <div id="analysisSection" class="border-t pt-8">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2 text-blue-600"></i>
                            Step 3: Get Your Analysis
                        </h3>
                        <p class="text-gray-600 mb-6">
                            Ready to analyze? Our AI will compare your resume with the job requirements and provide detailed insights.
                        </p>
                        <button
                            type="submit"
                            id="analyzeBtn"
                            class="btn-holographic disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled
                        >
                            <i class="fas fa-magic mr-2"></i>
                            <span id="analyzeText">🚀 Analyze Match</span>
                        </button>
                        <div class="mt-4 text-sm text-gray-500">
                            <i class="fas fa-lock mr-1"></i>
                            Your data is processed securely and not stored permanently
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Enhanced Loading State -->
        <div id="loadingState" class="hidden relative">
            <!-- Glowing container -->
            <div class="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl blur opacity-30 animate-pulse"></div>

            <div class="relative bg-white/10 backdrop-blur-2xl border border-white/20 rounded-2xl p-12 text-center shadow-2xl">
                <!-- AI Brain Animation -->
                <div class="mb-8 relative">
                    <div class="relative mx-auto w-24 h-24">
                        <!-- Outer rotating ring -->
                        <div class="absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full animate-spin"></div>
                        <!-- Middle rotating ring -->
                        <div class="absolute inset-2 border-4 border-transparent border-t-purple-500 border-r-pink-500 rounded-full animate-spin" style="animation-direction: reverse; animation-duration: 1.5s;"></div>
                        <!-- Inner pulsing core -->
                        <div class="absolute inset-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse flex items-center justify-center">
                            <i class="fas fa-brain text-white text-xl animate-bounce"></i>
                        </div>

                        <!-- Floating particles around brain -->
                        <div class="absolute -inset-4">
                            <div class="absolute top-0 left-1/2 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
                            <div class="absolute top-1/2 right-0 w-2 h-2 bg-purple-400 rounded-full animate-ping" style="animation-delay: 0.5s;"></div>
                            <div class="absolute bottom-0 left-1/2 w-2 h-2 bg-pink-400 rounded-full animate-ping" style="animation-delay: 1s;"></div>
                            <div class="absolute top-1/2 left-0 w-2 h-2 bg-cyan-400 rounded-full animate-ping" style="animation-delay: 1.5s;"></div>
                        </div>
                    </div>
                </div>

                <!-- Loading text with gradient -->
                <h3 class="text-3xl font-bold mb-4">
                    <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse">
                        Analyzing Your Resume...
                    </span>
                </h3>

                <p class="text-blue-200/80 mb-8 text-lg">
                    Our AI is comparing your resume with the job requirements using advanced machine learning
                </p>

                <!-- Enhanced Progress Steps -->
                <div class="max-w-lg mx-auto space-y-4">
                    <div id="loadingStep1" class="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="text-green-300 font-medium">Processing resume content</span>
                        </div>
                        <div class="text-green-400 text-sm">✓ Complete</div>
                    </div>

                    <div id="loadingStep2" class="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            </div>
                            <span class="text-blue-300 font-medium">Analyzing job requirements</span>
                        </div>
                        <div class="text-blue-400 text-sm animate-pulse">Processing...</div>
                    </div>

                    <div id="loadingStep3" class="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10 opacity-50">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-clock text-white text-sm"></i>
                            </div>
                            <span class="text-gray-300 font-medium">Generating recommendations</span>
                        </div>
                        <div class="text-gray-400 text-sm">Waiting...</div>
                    </div>
                </div>

                <!-- Progress bar -->
                <div class="mt-8 max-w-md mx-auto">
                    <div class="flex justify-between text-sm text-blue-200 mb-2">
                        <span>Progress</span>
                        <span id="loadingProgress">33%</span>
                    </div>
                    <div class="w-full bg-white/10 rounded-full h-3 overflow-hidden">
                        <div id="loadingProgressBar" class="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full transition-all duration-1000 w-1/3 relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
                        </div>
                    </div>
                </div>

                <!-- Fun facts while loading -->
                <div class="mt-8 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-400/20">
                    <div class="text-blue-200 text-sm">
                        <i class="fas fa-lightbulb text-yellow-400 mr-2"></i>
                        <span id="loadingTip">Did you know? Our AI analyzes over 50 different factors in your resume!</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Results Section -->
        <div id="resultsSection" class="hidden space-y-8">
            <!-- Results Header with Celebration -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-xl border border-green-400/30 rounded-full text-green-200 text-sm font-medium mb-4 shadow-2xl">
                    <i class="fas fa-check-circle mr-2 animate-pulse"></i>
                    <span>Analysis Complete</span>
                    <div class="ml-2 w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
                </div>
                <h3 class="text-4xl font-bold mb-2">
                    <span class="bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
                        Your Resume Analysis
                    </span>
                </h3>
                <p class="text-blue-200/80">Here's how your resume matches the job requirements</p>
            </div>

            <!-- Enhanced Match Score Card -->
            <div class="relative group">
                <div class="absolute -inset-1 bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>

                <div class="relative bg-white/10 backdrop-blur-2xl border border-white/20 rounded-2xl p-8 shadow-2xl">
                    <div class="text-center mb-8">
                        <!-- Enhanced circular progress -->
                        <div class="relative inline-block mb-6">
                            <div class="w-48 h-48 mx-auto relative">
                                <!-- Outer glow ring -->
                                <div class="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 opacity-20 animate-pulse"></div>

                                <!-- Main progress circle -->
                                <svg class="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                                    <!-- Background circle -->
                                    <path class="text-white/20" stroke="currentColor" stroke-width="2" fill="none"
                                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                                    <!-- Progress circle with gradient -->
                                    <defs>
                                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                            <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                    <path id="matchScoreCircle" stroke="url(#progressGradient)" stroke-width="3" fill="none"
                                          stroke-linecap="round" stroke-dasharray="0, 100"
                                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                          filter="drop-shadow(0 0 10px rgba(59, 130, 246, 0.5))"></path>
                                </svg>

                                <!-- Center content -->
                                <div class="absolute inset-0 flex flex-col items-center justify-center">
                                    <span id="matchPercentage" class="text-5xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent mb-2">0%</span>
                                    <span class="text-blue-200 text-sm font-medium">Match Score</span>
                                </div>

                                <!-- Floating particles around circle -->
                                <div class="absolute inset-0 pointer-events-none">
                                    <div class="absolute top-4 right-8 w-3 h-3 bg-blue-400 rounded-full animate-ping opacity-75"></div>
                                    <div class="absolute bottom-8 left-4 w-2 h-2 bg-purple-400 rounded-full animate-ping opacity-75" style="animation-delay: 1s;"></div>
                                    <div class="absolute top-12 left-8 w-2 h-2 bg-pink-400 rounded-full animate-ping opacity-75" style="animation-delay: 2s;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Score interpretation -->
                        <div id="scoreInterpretation" class="mb-6">
                            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium">
                                <i id="scoreIcon" class="mr-2"></i>
                                <span id="scoreText"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Overall assessment with better styling -->
                    <div class="bg-white/5 backdrop-blur-xl rounded-xl p-6 border border-white/10">
                        <h4 class="text-lg font-semibold text-white mb-3 flex items-center">
                            <i class="fas fa-comment-alt text-blue-400 mr-2"></i>
                            AI Assessment
                        </h4>
                        <p id="overallAssessment" class="text-blue-100/90 leading-relaxed"></p>
                    </div>
                </div>
            </div>

            <!-- Enhanced Detailed Analysis -->
            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <!-- Strengths Card -->
                <div class="relative group">
                    <div class="absolute -inset-1 bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>

                    <div class="relative bg-white/10 backdrop-blur-2xl border border-white/20 rounded-2xl p-6 shadow-2xl hover:bg-white/15 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                                <i class="fas fa-trophy text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-white">Your Strengths</h4>
                                <p class="text-green-200 text-sm">What makes you stand out</p>
                            </div>
                        </div>

                        <ul id="strengthsList" class="space-y-3"></ul>

                        <!-- Strength meter -->
                        <div class="mt-6 p-4 bg-green-500/10 rounded-xl border border-green-400/20">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-green-200 text-sm font-medium">Strength Level</span>
                                <span id="strengthPercentage" class="text-green-300 text-sm font-bold">0%</span>
                            </div>
                            <div class="w-full bg-white/10 rounded-full h-2">
                                <div id="strengthBar" class="h-full bg-gradient-to-r from-green-500 to-emerald-400 rounded-full transition-all duration-1000 w-0 relative">
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Areas for Improvement Card -->
                <div class="relative group">
                    <div class="absolute -inset-1 bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-300"></div>

                    <div class="relative bg-white/10 backdrop-blur-2xl border border-white/20 rounded-2xl p-6 shadow-2xl hover:bg-white/15 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                                <i class="fas fa-target text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-white">Areas for Improvement</h4>
                                <p class="text-orange-200 text-sm">Skills to focus on next</p>
                            </div>
                        </div>

                        <ul id="missingSkillsList" class="space-y-3"></ul>

                        <!-- Improvement potential meter -->
                        <div class="mt-6 p-4 bg-orange-500/10 rounded-xl border border-orange-400/20">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-orange-200 text-sm font-medium">Improvement Potential</span>
                                <span id="improvementPercentage" class="text-orange-300 text-sm font-bold">0%</span>
                            </div>
                            <div class="w-full bg-white/10 rounded-full h-2">
                                <div id="improvementBar" class="h-full bg-gradient-to-r from-orange-500 to-red-400 rounded-full transition-all duration-1000 w-0 relative">
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h4 class="text-xl font-bold text-blue-700 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Personalized Recommendations
                </h4>
                <ul id="recommendationsList" class="space-y-3"></ul>
            </div>

            <!-- Experience & Education Analysis -->
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Experience Analysis -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-briefcase mr-2"></i>
                        Experience Analysis
                    </h4>
                    <div id="experienceAnalysis" class="space-y-3"></div>
                </div>

                <!-- Education Analysis -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Education Analysis
                    </h4>
                    <div id="educationAnalysis" class="space-y-3"></div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center mt-8 space-y-4">
                <div class="flex flex-wrap justify-center gap-4">
                    <button
                        id="newAnalysisBtn"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        <i class="fas fa-plus mr-2"></i>
                        Analyze Another Resume
                    </button>
                    <button
                        id="downloadReportBtn"
                        class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        <i class="fas fa-download mr-2"></i>
                        Download Report
                    </button>
                    <button
                        id="shareResultsBtn"
                        class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        <i class="fas fa-share mr-2"></i>
                        Share Results
                    </button>
                </div>

                <!-- Feedback Section -->
                <div class="mt-8 p-6 bg-gray-50 rounded-lg">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-star mr-2 text-yellow-500"></i>
                        How was your experience?
                    </h4>
                    <div class="flex justify-center space-x-2 mb-4">
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="1">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="2">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="3">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="4">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="5">⭐</button>
                    </div>
                    <p class="text-sm text-gray-600 text-center">
                        Your feedback helps us improve the AI analysis quality
                    </p>
                </div>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden bg-red-50 border border-red-200 rounded-lg p-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle text-red-600 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">Analysis Failed</h3>
                    <p id="errorMessage" class="text-red-700 mt-1"></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-300">
                Built with <i class="fas fa-heart text-red-500"></i> for the AWS Lambda Hackathon 2024
            </p>
            <p class="text-gray-400 text-sm mt-2">
                Powered by AWS Lambda, Amazon Bedrock (Claude), and modern web technologies
            </p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

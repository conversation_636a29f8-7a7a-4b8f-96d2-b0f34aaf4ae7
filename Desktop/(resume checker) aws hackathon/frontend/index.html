<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume-to-Job Match Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">Resume Match Analyzer</h1>
                </div>
                <div class="text-sm text-gray-500">
                    <i class="fas fa-aws text-orange-500 mr-1"></i>
                    AWS Lambda Hackathon Entry
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <div class="mb-6">
                <div class="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-full text-blue-700 text-sm font-medium mb-4">
                    <i class="fas fa-robot mr-2"></i>
                    AI-Powered Resume Analysis
                </div>
            </div>
            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                Discover How Well Your Resume Matches Any Job
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Upload your resume and paste a job description to get AI-powered insights on your match score,
                missing skills, and personalized recommendations for improvement.
            </p>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto mb-8">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">⚡</div>
                    <div class="text-sm text-gray-600">Instant Analysis</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">🎯</div>
                    <div class="text-sm text-gray-600">Accurate Matching</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">💡</div>
                    <div class="text-sm text-gray-600">Smart Recommendations</div>
                </div>
            </div>
        </div>

        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
                <div id="step1" class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <span class="ml-2 text-sm font-medium text-blue-600">Upload Resume</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 rounded"></div>
                <div id="step2" class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Add Job Description</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 rounded"></div>
                <div id="step3" class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Get Analysis</span>
                </div>
            </div>
        </div>

        <!-- Analysis Form -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <form id="analysisForm" class="space-y-8">
                <!-- Resume Upload Section -->
                <div id="resumeSection">
                    <div class="flex items-center justify-between mb-4">
                        <label class="block text-lg font-semibold text-gray-900">
                            <i class="fas fa-file-upload mr-2 text-blue-600"></i>
                            Step 1: Upload Your Resume
                        </label>
                        <div id="resumeStatus" class="hidden">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Complete
                            </span>
                        </div>
                    </div>

                    <!-- Upload Methods Tabs -->
                    <div class="mb-6">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button type="button" id="fileTab" class="upload-tab active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                    <i class="fas fa-upload mr-2"></i>Upload File
                                </button>
                                <button type="button" id="textTab" class="upload-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <i class="fas fa-edit mr-2"></i>Paste Text
                                </button>
                                <button type="button" id="sampleTab" class="upload-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                    <i class="fas fa-eye mr-2"></i>Try Sample
                                </button>
                            </nav>
                        </div>
                    </div>

                    <!-- File Upload Tab -->
                    <div id="fileUploadTab" class="upload-content">
                        <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-all duration-200 cursor-pointer group">
                            <div class="group-hover:scale-105 transition-transform duration-200">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4 group-hover:text-blue-500"></i>
                                <p class="text-lg text-gray-600 mb-2">Drop your resume here or click to browse</p>
                                <p class="text-sm text-gray-500">Supports PDF, DOCX, and TXT files (max 5MB)</p>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        Secure & Private
                                    </span>
                                </div>
                            </div>
                            <input type="file" id="resumeFile" accept=".pdf,.docx,.txt" class="hidden">
                        </div>
                        <div id="fileInfo" class="mt-4 hidden">
                            <div class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg">
                                <i class="fas fa-file-check text-green-600 mr-3 text-xl"></i>
                                <div class="flex-1">
                                    <span id="fileName" class="text-green-800 font-medium"></span>
                                    <div id="fileSize" class="text-green-600 text-sm"></div>
                                </div>
                                <button type="button" id="removeFile" class="ml-auto text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-50">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Text Input Tab -->
                    <div id="textInputTab" class="upload-content hidden">
                        <div class="relative">
                            <textarea
                                id="resumeText"
                                rows="12"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                placeholder="Paste your resume content here...

Example:
John Smith
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE
Senior Developer at TechCorp (2020-Present)
- Developed web applications using React and Node.js
- Led team of 3 developers
..."
                            ></textarea>
                            <div id="textCounter" class="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
                                0 characters
                            </div>
                        </div>
                    </div>

                    <!-- Sample Resume Tab -->
                    <div id="sampleResumeTab" class="upload-content hidden">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                <h3 class="text-lg font-medium text-blue-900">Try with Sample Resume</h3>
                            </div>
                            <p class="text-blue-700 mb-4">
                                Want to see how it works? Use our sample resume to test the analysis feature.
                            </p>
                            <button type="button" id="loadSampleBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                Load Sample Resume
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Description Section -->
                <div id="jobSection">
                    <div class="flex items-center justify-between mb-4">
                        <label class="block text-lg font-semibold text-gray-900">
                            <i class="fas fa-briefcase mr-2 text-blue-600"></i>
                            Step 2: Add Job Description
                        </label>
                        <div id="jobStatus" class="hidden">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Complete
                            </span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <!-- Job Description Input -->
                        <div class="relative">
                            <textarea
                                id="jobDescription"
                                rows="12"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
                                placeholder="Paste the complete job description here...

Example:
Senior Software Engineer - Full Stack
Company: TechCorp Inc.

Requirements:
- 5+ years of experience in web development
- Proficiency in React, Node.js, and Python
- Experience with AWS cloud services
- Bachelor's degree in Computer Science

Responsibilities:
- Develop and maintain web applications
- Collaborate with cross-functional teams
- Mentor junior developers
..."
                            ></textarea>
                            <div id="jobCounter" class="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
                                0 characters
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex flex-wrap gap-2">
                            <button type="button" id="loadSampleJobBtn" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-file-alt mr-1"></i>
                                Load Sample Job
                            </button>
                            <button type="button" id="clearJobBtn" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                <i class="fas fa-eraser mr-1"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Analysis Section -->
                <div id="analysisSection" class="border-t pt-8">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2 text-blue-600"></i>
                            Step 3: Get Your Analysis
                        </h3>
                        <p class="text-gray-600 mb-6">
                            Ready to analyze? Our AI will compare your resume with the job requirements and provide detailed insights.
                        </p>
                        <button
                            type="submit"
                            id="analyzeBtn"
                            class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 shadow-lg"
                            disabled
                        >
                            <i class="fas fa-magic mr-2"></i>
                            <span id="analyzeText">Analyze Match</span>
                        </button>
                        <div class="mt-4 text-sm text-gray-500">
                            <i class="fas fa-lock mr-1"></i>
                            Your data is processed securely and not stored permanently
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="hidden bg-white rounded-lg shadow-lg p-8 text-center">
            <div class="mb-6">
                <div class="relative">
                    <div class="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-brain text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Analyzing Your Resume...</h3>
            <p class="text-gray-600 mb-4">Our AI is comparing your resume with the job requirements. This may take a few moments.</p>

            <!-- Progress Steps -->
            <div class="max-w-md mx-auto">
                <div class="space-y-2 text-sm text-gray-500">
                    <div id="loadingStep1" class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Processing resume content</span>
                    </div>
                    <div id="loadingStep2" class="flex items-center">
                        <div class="animate-spin w-4 h-4 border-2 border-blue-200 border-t-blue-500 rounded-full mr-2"></div>
                        <span>Analyzing job requirements</span>
                    </div>
                    <div id="loadingStep3" class="flex items-center text-gray-400">
                        <i class="fas fa-circle mr-2 text-xs"></i>
                        <span>Generating recommendations</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <!-- Match Score Card -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Match Analysis Results</h3>
                    <div class="relative inline-block">
                        <div class="w-32 h-32 mx-auto">
                            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                                <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                <path id="matchScoreCircle" class="text-blue-600" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="0, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            </svg>
                        </div>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span id="matchPercentage" class="text-3xl font-bold text-gray-900">0%</span>
                        </div>
                    </div>
                </div>
                <p id="overallAssessment" class="text-lg text-gray-700 text-center max-w-3xl mx-auto"></p>
            </div>

            <!-- Detailed Analysis -->
            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <!-- Strengths -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-green-700 mb-4">
                        <i class="fas fa-check-circle mr-2"></i>
                        Your Strengths
                    </h4>
                    <ul id="strengthsList" class="space-y-3"></ul>
                </div>

                <!-- Missing Skills -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-red-700 mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Areas for Improvement
                    </h4>
                    <ul id="missingSkillsList" class="space-y-3"></ul>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h4 class="text-xl font-bold text-blue-700 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Personalized Recommendations
                </h4>
                <ul id="recommendationsList" class="space-y-3"></ul>
            </div>

            <!-- Experience & Education Analysis -->
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Experience Analysis -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-briefcase mr-2"></i>
                        Experience Analysis
                    </h4>
                    <div id="experienceAnalysis" class="space-y-3"></div>
                </div>

                <!-- Education Analysis -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h4 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Education Analysis
                    </h4>
                    <div id="educationAnalysis" class="space-y-3"></div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center mt-8 space-y-4">
                <div class="flex flex-wrap justify-center gap-4">
                    <button
                        id="newAnalysisBtn"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        <i class="fas fa-plus mr-2"></i>
                        Analyze Another Resume
                    </button>
                    <button
                        id="downloadReportBtn"
                        class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        <i class="fas fa-download mr-2"></i>
                        Download Report
                    </button>
                    <button
                        id="shareResultsBtn"
                        class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        <i class="fas fa-share mr-2"></i>
                        Share Results
                    </button>
                </div>

                <!-- Feedback Section -->
                <div class="mt-8 p-6 bg-gray-50 rounded-lg">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-star mr-2 text-yellow-500"></i>
                        How was your experience?
                    </h4>
                    <div class="flex justify-center space-x-2 mb-4">
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="1">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="2">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="3">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="4">⭐</button>
                        <button class="feedback-star text-2xl text-gray-300 hover:text-yellow-500" data-rating="5">⭐</button>
                    </div>
                    <p class="text-sm text-gray-600 text-center">
                        Your feedback helps us improve the AI analysis quality
                    </p>
                </div>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden bg-red-50 border border-red-200 rounded-lg p-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle text-red-600 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">Analysis Failed</h3>
                    <p id="errorMessage" class="text-red-700 mt-1"></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-300">
                Built with <i class="fas fa-heart text-red-500"></i> for the AWS Lambda Hackathon 2024
            </p>
            <p class="text-gray-400 text-sm mt-2">
                Powered by AWS Lambda, Amazon Bedrock (Claude), and modern web technologies
            </p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

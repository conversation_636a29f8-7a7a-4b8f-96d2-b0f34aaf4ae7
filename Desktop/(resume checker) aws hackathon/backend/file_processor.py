"""
File Processor Lambda Function
Triggered by S3 events when files are uploaded
"""

import json
import logging
import os
import urllib.parse
from typing import Dict, Any
import boto3
from utils.analytics_handler import Analytics<PERSON>andler
from utils.resume_parser import ResumeParser

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize handlers
analytics_handler = AnalyticsHandler()
resume_parser = ResumeParser()
s3_client = boto3.client('s3')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Lambda handler for S3 file upload events
    Processes uploaded resume files and extracts metadata
    """
    logger.info("Processing S3 file upload event...")
    
    try:
        # Process each record in the event
        processed_files = []
        
        for record in event.get('Records', []):
            if record.get('eventSource') == 'aws:s3':
                result = process_s3_record(record)
                processed_files.append(result)
        
        logger.info(f"Processed {len(processed_files)} files")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'status': 'success',
                'processed_files': processed_files,
                'total_processed': len(processed_files)
            })
        }
        
    except Exception as e:
        logger.error(f"Error processing S3 event: {str(e)}")
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'status': 'error',
                'error': str(e)
            })
        }

def process_s3_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """Process a single S3 record"""
    
    try:
        # Extract S3 information
        s3_info = record['s3']
        bucket_name = s3_info['bucket']['name']
        object_key = urllib.parse.unquote_plus(s3_info['object']['key'])
        object_size = s3_info['object']['size']
        
        logger.info(f"Processing file: {object_key} ({object_size} bytes)")
        
        # Download file from S3
        response = s3_client.get_object(Bucket=bucket_name, Key=object_key)
        file_content = response['Body'].read()
        
        # Extract file metadata
        file_metadata = extract_file_metadata(object_key, file_content, object_size)
        
        # Log file processing event to analytics
        log_file_event(object_key, file_metadata)
        
        return {
            'file_key': object_key,
            'status': 'processed',
            'metadata': file_metadata
        }
        
    except Exception as e:
        logger.error(f"Error processing S3 record: {str(e)}")
        return {
            'file_key': record.get('s3', {}).get('object', {}).get('key', 'unknown'),
            'status': 'error',
            'error': str(e)
        }

def extract_file_metadata(file_key: str, file_content: bytes, file_size: int) -> Dict[str, Any]:
    """Extract metadata from uploaded file"""
    
    try:
        # Determine file type
        file_extension = file_key.split('.')[-1].lower() if '.' in file_key else 'unknown'
        
        metadata = {
            'file_name': file_key.split('/')[-1],
            'file_type': file_extension,
            'file_size': file_size,
            'upload_path': file_key
        }
        
        # Extract text content if it's a resume file
        if file_extension in ['pdf', 'docx', 'txt']:
            try:
                if file_extension == 'pdf':
                    text_content = resume_parser.extract_text_from_pdf(file_content)
                elif file_extension == 'txt':
                    text_content = file_content.decode('utf-8')
                else:
                    text_content = "Content extraction not implemented for this file type"
                
                # Analyze text content
                text_stats = analyze_text_content(text_content)
                metadata.update(text_stats)
                
            except Exception as e:
                logger.warning(f"Failed to extract text from {file_key}: {str(e)}")
                metadata['text_extraction_error'] = str(e)
        
        return metadata
        
    except Exception as e:
        logger.error(f"Error extracting metadata: {str(e)}")
        return {
            'file_name': file_key,
            'error': str(e)
        }

def analyze_text_content(text: str) -> Dict[str, Any]:
    """Analyze text content and extract statistics"""
    
    try:
        # Basic text statistics
        word_count = len(text.split())
        char_count = len(text)
        line_count = len(text.split('\n'))
        
        # Look for common resume sections
        sections_found = []
        section_keywords = {
            'experience': ['experience', 'work history', 'employment', 'career'],
            'education': ['education', 'degree', 'university', 'college', 'school'],
            'skills': ['skills', 'technologies', 'programming', 'languages'],
            'contact': ['email', 'phone', 'address', 'linkedin', 'github']
        }
        
        text_lower = text.lower()
        for section, keywords in section_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                sections_found.append(section)
        
        # Look for common technologies/skills
        tech_keywords = [
            'python', 'javascript', 'java', 'react', 'node.js', 'aws', 'docker',
            'kubernetes', 'sql', 'git', 'html', 'css', 'machine learning', 'ai'
        ]
        
        technologies_found = [
            tech for tech in tech_keywords 
            if tech.lower() in text_lower
        ]
        
        return {
            'word_count': word_count,
            'character_count': char_count,
            'line_count': line_count,
            'sections_found': sections_found,
            'technologies_mentioned': technologies_found,
            'estimated_completeness': calculate_completeness_score(sections_found)
        }
        
    except Exception as e:
        logger.error(f"Error analyzing text content: {str(e)}")
        return {
            'analysis_error': str(e)
        }

def calculate_completeness_score(sections_found: list) -> int:
    """Calculate a completeness score based on resume sections found"""
    
    required_sections = ['experience', 'education', 'skills', 'contact']
    found_count = len([s for s in sections_found if s in required_sections])
    
    # Score out of 100
    completeness_score = (found_count / len(required_sections)) * 100
    
    return int(completeness_score)

def log_file_event(file_key: str, metadata: Dict[str, Any]) -> None:
    """Log file processing event to analytics"""
    
    try:
        # Create a simple analytics record for file uploads
        event_data = {
            'event_type': 'file_upload',
            'file_key': file_key,
            'file_metadata': metadata,
            'processing_status': 'completed'
        }
        
        logger.info(f"File processing event: {json.dumps(event_data)}")
        
        # In a real implementation, you might want to store this in DynamoDB
        # For now, we'll just log it
        
    except Exception as e:
        logger.warning(f"Failed to log file event: {str(e)}")

def generate_file_insights(metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Generate insights about the uploaded file"""
    
    insights = []
    
    # File size insights
    file_size = metadata.get('file_size', 0)
    if file_size > 1024 * 1024:  # > 1MB
        insights.append({
            'type': 'warning',
            'message': 'Large file size - consider optimizing for faster processing',
            'icon': '📄'
        })
    elif file_size < 1024:  # < 1KB
        insights.append({
            'type': 'warning',
            'message': 'Very small file - may not contain enough information',
            'icon': '📝'
        })
    
    # Completeness insights
    completeness = metadata.get('estimated_completeness', 0)
    if completeness >= 75:
        insights.append({
            'type': 'success',
            'message': 'Resume appears complete with all major sections',
            'icon': '✅'
        })
    elif completeness >= 50:
        insights.append({
            'type': 'info',
            'message': 'Resume is partially complete - consider adding missing sections',
            'icon': '📋'
        })
    else:
        insights.append({
            'type': 'warning',
            'message': 'Resume appears incomplete - missing important sections',
            'icon': '⚠️'
        })
    
    # Technology insights
    tech_count = len(metadata.get('technologies_mentioned', []))
    if tech_count >= 5:
        insights.append({
            'type': 'success',
            'message': f'Good technical diversity - {tech_count} technologies mentioned',
            'icon': '🔧'
        })
    elif tech_count >= 2:
        insights.append({
            'type': 'info',
            'message': f'Some technical skills mentioned - {tech_count} technologies found',
            'icon': '💻'
        })
    else:
        insights.append({
            'type': 'warning',
            'message': 'Few technical skills mentioned - consider adding more',
            'icon': '🛠️'
        })
    
    return {
        'insights': insights,
        'summary': {
            'completeness_score': completeness,
            'technology_count': tech_count,
            'file_size_mb': round(file_size / (1024 * 1024), 2)
        }
    }

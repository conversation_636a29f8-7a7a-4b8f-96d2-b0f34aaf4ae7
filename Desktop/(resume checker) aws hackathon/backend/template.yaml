AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Resume-to-Job-Match Analyzer
  AWS Lambda Hackathon Project - Serverless Resume Analysis with AI

# Global configuration
Globals:
  Function:
    Timeout: 300
    MemorySize: 1024
    Runtime: python3.12
    Environment:
      Variables:
        CORS_ORIGIN: "*"
        LOG_LEVEL: INFO

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

Resources:
  # S3 Bucket for file uploads
  ResumeUploadBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "resume-analyzer-uploads-${Environment}-${AWS::AccountId}"
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders: ["*"]
            AllowedMethods: [GET, PUT, POST, DELETE, HEAD]
            AllowedOrigins: ["*"]
            MaxAge: 3000
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldFiles
            Status: Enabled
            ExpirationInDays: 7

  # DynamoDB Table for analysis results with advanced features
  AnalysisResultsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "resume-analysis-results-${Environment}"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: analysis_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: match_score
          AttributeType: N
        - AttributeName: job_category
          AttributeType: S
        - AttributeName: status
          AttributeType: S
      KeySchema:
        - AttributeName: analysis_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: CreatedAtIndex
          KeySchema:
            - AttributeName: created_at
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: UserAnalysisIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ScoreIndex
          KeySchema:
            - AttributeName: match_score
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: CategoryIndex
          KeySchema:
            - AttributeName: job_category
              KeyType: HASH
            - AttributeName: match_score
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: StatusIndex
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: ResumeAnalyzer
        - Key: Purpose
          Value: AnalysisResults

  # API Gateway
  ResumeAnalyzerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      GatewayResponses:
        DEFAULT_4XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        DEFAULT_5XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"

  # Lambda Function: File Upload Handler
  FileUploadFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-upload-${Environment}"
      CodeUri: src/
      Handler: upload_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          RESULTS_TABLE: !Ref AnalysisResultsTable
      Policies:
        - S3WritePolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBWritePolicy:
            TableName: !Ref AnalysisResultsTable
      Events:
        UploadApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /upload
            Method: post

  # Lambda Function: Resume Analysis Handler
  AnalysisFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-analysis-${Environment}"
      CodeUri: src/
      Handler: analysis_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          RESULTS_TABLE: !Ref AnalysisResultsTable
          BEDROCK_REGION: !Ref AWS::Region
      Policies:
        - S3ReadPolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBWritePolicy:
            TableName: !Ref AnalysisResultsTable
        - Statement:
            - Effect: Allow
              Action:
                - bedrock:InvokeModel
                - bedrock:InvokeModelWithResponseStream
              Resource: 
                - !Sub "arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0"
                - !Sub "arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-haiku-20240307-v1:0"
        - Statement:
            - Effect: Allow
              Action:
                - textract:DetectDocumentText
                - textract:AnalyzeDocument
              Resource: "*"
        - Statement:
            - Effect: Allow
              Action:
                - comprehend:DetectSentiment
                - comprehend:DetectKeyPhrases
                - comprehend:DetectEntities
              Resource: "*"
      Events:
        AnalysisApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /analyze
            Method: post

  # Lambda Function: Results Retrieval Handler
  ResultsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-results-${Environment}"
      CodeUri: src/
      Handler: results_handler.lambda_handler
      Environment:
        Variables:
          RESULTS_TABLE: !Ref AnalysisResultsTable
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref AnalysisResultsTable
      Events:
        GetResultsApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /results/{analysis_id}
            Method: get
        ListResultsApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /results
            Method: get

  # Lambda Function: Health Check
  HealthCheckFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-health-${Environment}"
      CodeUri: src/
      Handler: health_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          RESULTS_TABLE: !Ref AnalysisResultsTable
          BEDROCK_REGION: !Ref AWS::Region
      Policies:
        - S3ReadPolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBReadPolicy:
            TableName: !Ref AnalysisResultsTable
        - Statement:
            - Effect: Allow
              Action:
                - bedrock:ListFoundationModels
              Resource: "*"
      Events:
        HealthApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /health
            Method: get

Outputs:
  # API Gateway endpoint URL
  ApiGatewayUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ResumeAnalyzerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"

  # S3 Bucket Name
  UploadBucketName:
    Description: "S3 bucket for file uploads"
    Value: !Ref ResumeUploadBucket
    Export:
      Name: !Sub "${AWS::StackName}-UploadBucket"

  # DynamoDB Table Name
  ResultsTableName:
    Description: "DynamoDB table for analysis results"
    Value: !Ref AnalysisResultsTable
    Export:
      Name: !Sub "${AWS::StackName}-ResultsTable"

  # Region
  Region:
    Description: "AWS Region"
    Value: !Ref AWS::Region
    Export:
      Name: !Sub "${AWS::StackName}-Region"

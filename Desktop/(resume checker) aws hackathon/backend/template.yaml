AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Resume-to-Job-Match Analyzer
  AWS Lambda Hackathon Project - Serverless Resume Analysis with AI

# Global configuration
Globals:
  Function:
    Timeout: 300
    MemorySize: 1024
    Runtime: python3.12
    Environment:
      Variables:
        CORS_ORIGIN: "*"
        LOG_LEVEL: INFO

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

  GeminiApiKey:
    Type: String
    NoEcho: true
    Description: Google Gemini API Key for enhanced AI analysis
    Default: "your-gemini-api-key-here"

Resources:
  # S3 Bucket for file uploads
  ResumeUploadBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "resume-analyzer-uploads-${Environment}-${AWS::AccountId}"
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders: ["*"]
            AllowedMethods: [GET, PUT, POST, DELETE, HEAD]
            AllowedOrigins: ["*"]
            MaxAge: 3000
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldFiles
            Status: Enabled
            ExpirationInDays: 7

  # Users Table for authentication and user management
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "resume-analyzer-users-${Environment}"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: CreatedAtIndex
          KeySchema:
            - AttributeName: created_at
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  # File Metadata Table for enhanced file management
  FileMetadataTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "resume-analyzer-files-${Environment}"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: file_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: uploaded_at
          AttributeType: S
        - AttributeName: file_type
          AttributeType: S
      KeySchema:
        - AttributeName: file_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserFilesIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: uploaded_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: FileTypeIndex
          KeySchema:
            - AttributeName: file_type
              KeyType: HASH
            - AttributeName: uploaded_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

  # DynamoDB Table for analysis results with advanced features
  AnalysisResultsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "resume-analysis-results-${Environment}"
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: analysis_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: match_score
          AttributeType: N
        - AttributeName: job_category
          AttributeType: S
        - AttributeName: status
          AttributeType: S
      KeySchema:
        - AttributeName: analysis_id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: CreatedAtIndex
          KeySchema:
            - AttributeName: created_at
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: UserAnalysisIndex
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ScoreIndex
          KeySchema:
            - AttributeName: match_score
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: CategoryIndex
          KeySchema:
            - AttributeName: job_category
              KeyType: HASH
            - AttributeName: match_score
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: StatusIndex
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: created_at
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: ResumeAnalyzer
        - Key: Purpose
          Value: AnalysisResults

  # AWS Cognito User Pool for Authentication
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub "resume-analyzer-users-${Environment}"
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: name
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: company
          AttributeDataType: String
          Required: false
          Mutable: true
      UserPoolTags:
        Environment: !Ref Environment
        Project: ResumeAnalyzer

  # Cognito User Pool Client
  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      UserPoolId: !Ref UserPool
      ClientName: !Sub "resume-analyzer-client-${Environment}"
      GenerateSecret: false
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - USER_PASSWORD_AUTH
        - ALLOW_USER_SRP_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      SupportedIdentityProviders:
        - COGNITO
      CallbackURLs:
        - http://localhost:8000
        - https://your-domain.com
      LogoutURLs:
        - http://localhost:8000
        - https://your-domain.com
      AllowedOAuthFlows:
        - code
        - implicit
      AllowedOAuthScopes:
        - email
        - openid
        - profile
      AllowedOAuthFlowsUserPoolClient: true

  # Cognito Identity Pool
  IdentityPool:
    Type: AWS::Cognito::IdentityPool
    Properties:
      IdentityPoolName: !Sub "resume-analyzer-identity-${Environment}"
      AllowUnauthenticatedIdentities: false
      CognitoIdentityProviders:
        - ClientId: !Ref UserPoolClient
          ProviderName: !GetAtt UserPool.ProviderName

  # A2I Human Review Workflow for Quality Assurance
  HumanReviewWorkflow:
    Type: AWS::SageMaker::FlowDefinition
    Properties:
      FlowDefinitionName: !Sub "resume-analysis-review-${Environment}"
      RoleArn: !GetAtt A2IExecutionRole.Arn
      HumanLoopConfig:
        WorkteamArn: !GetAtt ReviewWorkteam.WorkteamArn
        HumanTaskUiArn: !Ref HumanTaskUI
        TaskCount: 1
        TaskDescription: "Review and validate resume analysis results for accuracy"
        TaskTitle: "Resume Analysis Quality Review"
        TaskTimeLimitInSeconds: 3600
      OutputConfig:
        S3OutputPath: !Sub "s3://${ResumeUploadBucket}/a2i-output/"
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: ResumeAnalyzer

  # A2I Execution Role
  A2IExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: sagemaker.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonSageMakerFullAccess
      Policies:
        - PolicyName: A2IExecutionPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                Resource: !Sub "${ResumeUploadBucket}/*"

  # A2I Human Task UI
  HumanTaskUI:
    Type: AWS::SageMaker::HumanTaskUi
    Properties:
      HumanTaskUiName: !Sub "resume-review-ui-${Environment}"
      UiTemplate:
        Content: |
          <script src="https://assets.crowd.aws/crowd-html-elements.js"></script>
          <crowd-form>
            <crowd-instructions link-text="View instructions" link-type="button">
              <short-summary>Review the resume analysis results for accuracy and completeness.</short-summary>
              <detailed-instructions>
                <p>Please review the AI-generated resume analysis and verify:</p>
                <ol>
                  <li>Match score accuracy (0-100)</li>
                  <li>Identified skills are correct</li>
                  <li>Missing skills are relevant</li>
                  <li>Recommendations are helpful</li>
                </ol>
              </detailed-instructions>
            </crowd-instructions>

            <div>
              <h3>Resume Analysis Results</h3>
              <p><strong>Match Score:</strong> {{ task.input.match_score }}%</p>
              <p><strong>Overall Assessment:</strong> {{ task.input.overall_assessment }}</p>

              <h4>Identified Skills:</h4>
              <ul>
                {% for skill in task.input.skills %}
                <li>{{ skill }}</li>
                {% endfor %}
              </ul>

              <h4>Recommendations:</h4>
              <ul>
                {% for rec in task.input.recommendations %}
                <li>{{ rec }}</li>
                {% endfor %}
              </ul>
            </div>

            <crowd-radio-group>
              <crowd-radio-button name="quality" value="excellent">Excellent Analysis</crowd-radio-button>
              <crowd-radio-button name="quality" value="good">Good Analysis</crowd-radio-button>
              <crowd-radio-button name="quality" value="needs_improvement">Needs Improvement</crowd-radio-button>
              <crowd-radio-button name="quality" value="poor">Poor Analysis</crowd-radio-button>
            </crowd-radio-group>

            <crowd-text-area name="feedback" rows="4" placeholder="Provide feedback on the analysis quality..."></crowd-text-area>
          </crowd-form>

  # A2I Review Workteam
  ReviewWorkteam:
    Type: AWS::SageMaker::Workteam
    Properties:
      WorkteamName: !Sub "resume-reviewers-${Environment}"
      Description: "Team of experts to review resume analysis results"
      MemberDefinitions:
        - CognitoMemberDefinition:
            UserPool: !Ref UserPool
            UserGroup: !Ref ReviewerGroup
            ClientId: !Ref UserPoolClient

  # Cognito User Group for Reviewers
  ReviewerGroup:
    Type: AWS::Cognito::UserPoolGroup
    Properties:
      GroupName: "resume-reviewers"
      UserPoolId: !Ref UserPool
      Description: "Group for A2I resume analysis reviewers"
      Precedence: 1

  # API Gateway
  ResumeAnalyzerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      GatewayResponses:
        DEFAULT_4XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        DEFAULT_5XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"

  # Lambda Function: File Upload Handler
  FileUploadFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-upload-${Environment}"
      CodeUri: src/
      Handler: upload_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          RESULTS_TABLE: !Ref AnalysisResultsTable
      Policies:
        - S3WritePolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBWritePolicy:
            TableName: !Ref AnalysisResultsTable
      Events:
        UploadApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /upload
            Method: post

  # Lambda Function: User Authentication Handler
  AuthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-auth-${Environment}"
      CodeUri: src/
      Handler: auth_handler.lambda_handler
      Environment:
        Variables:
          USER_POOL_ID: !Ref UserPool
          USER_POOL_CLIENT_ID: !Ref UserPoolClient
          USERS_TABLE: !Ref UsersTable
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminCreateUser
                - cognito-idp:AdminSetUserPassword
                - cognito-idp:AdminInitiateAuth
                - cognito-idp:AdminGetUser
                - cognito-idp:ListUsers
              Resource: !GetAtt UserPool.Arn
      Events:
        RegisterApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /auth/register
            Method: post
        LoginApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /auth/login
            Method: post
        ProfileApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /auth/profile
            Method: get

  # Lambda Function: Enhanced File Management
  FileManagementFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-files-${Environment}"
      CodeUri: src/
      Handler: file_management_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          FILES_TABLE: !Ref FileMetadataTable
          USERS_TABLE: !Ref UsersTable
      Policies:
        - S3CrudPolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBCrudPolicy:
            TableName: !Ref FileMetadataTable
        - DynamoDBReadPolicy:
            TableName: !Ref UsersTable
      Events:
        ListFilesApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /files
            Method: get
        DeleteFileApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /files/{file_id}
            Method: delete
        GetFileApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /files/{file_id}
            Method: get

  # Lambda Function: A2I Integration Handler
  A2IIntegrationFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-a2i-${Environment}"
      CodeUri: src/
      Handler: a2i_handler.lambda_handler
      Environment:
        Variables:
          FLOW_DEFINITION_ARN: !Ref HumanReviewWorkflow
          RESULTS_TABLE: !Ref AnalysisResultsTable
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref AnalysisResultsTable
        - Statement:
            - Effect: Allow
              Action:
                - sagemaker:StartHumanLoop
                - sagemaker:DescribeHumanLoop
                - sagemaker:StopHumanLoop
                - sagemaker:ListHumanLoops
              Resource: "*"
      Events:
        StartReviewApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /review/start
            Method: post
        ReviewStatusApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /review/{loop_name}
            Method: get

  # Lambda Function: Enhanced Resume Analysis Handler with Gemini Integration
  AnalysisFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-analysis-${Environment}"
      CodeUri: src/
      Handler: enhanced_analysis_handler.lambda_handler
      Timeout: 900
      MemorySize: 2048
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          RESULTS_TABLE: !Ref AnalysisResultsTable
          FILES_TABLE: !Ref FileMetadataTable
          USERS_TABLE: !Ref UsersTable
          BEDROCK_REGION: !Ref AWS::Region
          GEMINI_API_KEY: !Ref GeminiApiKey
          A2I_FLOW_ARN: !Ref HumanReviewWorkflow
      Policies:
        - S3ReadPolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBWritePolicy:
            TableName: !Ref AnalysisResultsTable
        - Statement:
            - Effect: Allow
              Action:
                - bedrock:InvokeModel
                - bedrock:InvokeModelWithResponseStream
              Resource: 
                - !Sub "arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0"
                - !Sub "arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-haiku-20240307-v1:0"
        - Statement:
            - Effect: Allow
              Action:
                - textract:DetectDocumentText
                - textract:AnalyzeDocument
              Resource: "*"
        - Statement:
            - Effect: Allow
              Action:
                - comprehend:DetectSentiment
                - comprehend:DetectKeyPhrases
                - comprehend:DetectEntities
              Resource: "*"
      Events:
        AnalysisApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /analyze
            Method: post

  # Lambda Function: Results Retrieval Handler
  ResultsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-results-${Environment}"
      CodeUri: src/
      Handler: results_handler.lambda_handler
      Environment:
        Variables:
          RESULTS_TABLE: !Ref AnalysisResultsTable
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref AnalysisResultsTable
      Events:
        GetResultsApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /results/{analysis_id}
            Method: get
        ListResultsApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /results
            Method: get

  # Lambda Function: Health Check
  HealthCheckFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "resume-analyzer-health-${Environment}"
      CodeUri: src/
      Handler: health_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET: !Ref ResumeUploadBucket
          RESULTS_TABLE: !Ref AnalysisResultsTable
          BEDROCK_REGION: !Ref AWS::Region
      Policies:
        - S3ReadPolicy:
            BucketName: !Ref ResumeUploadBucket
        - DynamoDBReadPolicy:
            TableName: !Ref AnalysisResultsTable
        - Statement:
            - Effect: Allow
              Action:
                - bedrock:ListFoundationModels
              Resource: "*"
      Events:
        HealthApi:
          Type: Api
          Properties:
            RestApiId: !Ref ResumeAnalyzerApi
            Path: /health
            Method: get

Outputs:
  # API Gateway endpoint URL
  ApiGatewayUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ResumeAnalyzerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"

  # S3 Bucket Name
  UploadBucketName:
    Description: "S3 bucket for file uploads"
    Value: !Ref ResumeUploadBucket
    Export:
      Name: !Sub "${AWS::StackName}-UploadBucket"

  # DynamoDB Table Name
  ResultsTableName:
    Description: "DynamoDB table for analysis results"
    Value: !Ref AnalysisResultsTable
    Export:
      Name: !Sub "${AWS::StackName}-ResultsTable"

  # Region
  Region:
    Description: "AWS Region"
    Value: !Ref AWS::Region
    Export:
      Name: !Sub "${AWS::StackName}-Region"

"""
Analytics Processor Lambda Function
Triggered by EventBridge on a schedule to process analytics data
"""

import json
import logging
import os
from typing import Dict, Any
from utils.analytics_handler import AnalyticsHandler

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize analytics handler
analytics_handler = AnalyticsHandler()

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Lambda handler for scheduled analytics processing
    Triggered by EventBridge every hour
    """
    logger.info("Starting scheduled analytics processing...")
    
    try:
        # Get analytics summary for the last 24 hours
        summary = analytics_handler.get_analytics_summary(hours=24)
        
        logger.info(f"Analytics Summary: {json.dumps(summary, indent=2)}")
        
        # Get trending skills for the last 7 days
        trending = analytics_handler.get_trending_skills(days=7)
        
        logger.info(f"Trending Skills: {json.dumps(trending, indent=2)}")
        
        # Cleanup old records (older than 30 days)
        cleanup_count = analytics_handler.cleanup_old_records(days_old=30)
        
        logger.info(f"Cleaned up {cleanup_count} old records")
        
        # Prepare response
        result = {
            'status': 'success',
            'analytics_summary': summary,
            'trending_skills': trending,
            'cleanup_count': cleanup_count,
            'processed_at': context.aws_request_id if context else 'local'
        }
        
        logger.info("Analytics processing completed successfully")
        
        return {
            'statusCode': 200,
            'body': json.dumps(result)
        }
        
    except Exception as e:
        logger.error(f"Error in analytics processing: {str(e)}")
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'status': 'error',
                'error': str(e)
            })
        }

def get_analytics_dashboard_data() -> Dict[str, Any]:
    """
    Get comprehensive analytics data for dashboard
    """
    try:
        # Get data for different time periods
        last_hour = analytics_handler.get_analytics_summary(hours=1)
        last_day = analytics_handler.get_analytics_summary(hours=24)
        last_week = analytics_handler.get_analytics_summary(hours=168)  # 7 days
        
        # Get trending skills
        trending = analytics_handler.get_trending_skills(days=7)
        
        dashboard_data = {
            'metrics': {
                'last_hour': last_hour,
                'last_day': last_day,
                'last_week': last_week
            },
            'trending': trending,
            'generated_at': context.aws_request_id if 'context' in globals() else 'local'
        }
        
        return dashboard_data
        
    except Exception as e:
        logger.error(f"Error generating dashboard data: {str(e)}")
        return {
            'error': str(e),
            'metrics': {},
            'trending': {}
        }

# Additional utility functions for analytics

def calculate_performance_metrics(summary: Dict[str, Any]) -> Dict[str, Any]:
    """Calculate performance metrics from analytics summary"""
    
    total_analyses = summary.get('total_analyses', 0)
    avg_match = summary.get('average_match_percentage', 0)
    
    # Calculate performance score
    if total_analyses == 0:
        performance_score = 0
    else:
        # Performance based on volume and quality
        volume_score = min(total_analyses / 100, 1.0)  # Max at 100 analyses
        quality_score = avg_match / 100  # Convert percentage to 0-1
        performance_score = (volume_score * 0.3 + quality_score * 0.7) * 100
    
    return {
        'performance_score': round(performance_score, 2),
        'volume_score': round(volume_score * 100, 2) if total_analyses > 0 else 0,
        'quality_score': round(avg_match, 2),
        'total_analyses': total_analyses
    }

def generate_insights(summary: Dict[str, Any]) -> list:
    """Generate insights from analytics data"""
    
    insights = []
    
    total_analyses = summary.get('total_analyses', 0)
    avg_match = summary.get('average_match_percentage', 0)
    match_dist = summary.get('match_distribution', {})
    
    # Volume insights
    if total_analyses > 50:
        insights.append({
            'type': 'success',
            'message': f'High activity: {total_analyses} analyses processed',
            'icon': '📈'
        })
    elif total_analyses > 10:
        insights.append({
            'type': 'info',
            'message': f'Moderate activity: {total_analyses} analyses processed',
            'icon': '📊'
        })
    else:
        insights.append({
            'type': 'warning',
            'message': f'Low activity: Only {total_analyses} analyses processed',
            'icon': '📉'
        })
    
    # Quality insights
    if avg_match > 75:
        insights.append({
            'type': 'success',
            'message': f'High match quality: {avg_match:.1f}% average match',
            'icon': '🎯'
        })
    elif avg_match > 50:
        insights.append({
            'type': 'info',
            'message': f'Good match quality: {avg_match:.1f}% average match',
            'icon': '✅'
        })
    else:
        insights.append({
            'type': 'warning',
            'message': f'Low match quality: {avg_match:.1f}% average match',
            'icon': '⚠️'
        })
    
    # Distribution insights
    excellent_count = match_dist.get('excellent', 0)
    poor_count = match_dist.get('poor', 0)
    
    if excellent_count > poor_count * 2:
        insights.append({
            'type': 'success',
            'message': f'Most resumes ({excellent_count}) are excellent matches',
            'icon': '🌟'
        })
    elif poor_count > excellent_count:
        insights.append({
            'type': 'warning',
            'message': f'Many resumes ({poor_count}) need improvement',
            'icon': '🔧'
        })
    
    return insights

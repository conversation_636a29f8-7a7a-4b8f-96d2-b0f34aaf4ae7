#!/bin/bash

# Resume Analyzer AWS Deployment Script
# AWS Lambda Hackathon Project

set -e  # Exit on any error

echo "🚀 Starting Resume Analyzer AWS Deployment..."

# Configuration
STACK_NAME="resume-analyzer-hackathon"
ENVIRONMENT="dev"
REGION="us-east-2"
S3_BUCKET_PREFIX="resume-analyzer-sam-deployments"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check SAM CLI
    if ! command -v sam &> /dev/null; then
        print_error "SAM CLI is not installed"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials not configured"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Create S3 bucket for SAM deployments
create_deployment_bucket() {
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    DEPLOYMENT_BUCKET="${S3_BUCKET_PREFIX}-${ACCOUNT_ID}-${REGION}"

    print_status "Checking deployment bucket: ${DEPLOYMENT_BUCKET}"

    # Check if bucket exists
    if ! aws s3 ls "s3://${DEPLOYMENT_BUCKET}" >/dev/null 2>&1; then
        print_status "Creating deployment bucket: ${DEPLOYMENT_BUCKET}"

        if [ "$REGION" = "us-east-1" ]; then
            aws s3 mb "s3://${DEPLOYMENT_BUCKET}" >/dev/null
        else
            aws s3 mb "s3://${DEPLOYMENT_BUCKET}" --region "$REGION" >/dev/null
        fi

        # Enable versioning
        aws s3api put-bucket-versioning \
            --bucket "$DEPLOYMENT_BUCKET" \
            --versioning-configuration Status=Enabled >/dev/null

        print_success "Deployment bucket created: ${DEPLOYMENT_BUCKET}"
    else
        print_status "Deployment bucket already exists: ${DEPLOYMENT_BUCKET}"
    fi

    echo "$DEPLOYMENT_BUCKET"
}

# Validate SAM template
validate_template() {
    print_status "Validating SAM template..."
    
    if sam validate --template template.yaml; then
        print_success "SAM template validation passed"
    else
        print_error "SAM template validation failed"
        exit 1
    fi
}

# Build SAM application
build_application() {
    print_status "Building SAM application..."

    # Try building without container first (faster for development)
    if sam build; then
        print_success "SAM build completed"
    else
        print_warning "SAM build without container failed, trying with container..."
        if sam build --use-container; then
            print_success "SAM build with container completed"
        else
            print_error "SAM build failed"
            exit 1
        fi
    fi
}

# Deploy SAM application
deploy_application() {
    local deployment_bucket=$1

    print_status "Deploying SAM application..."
    print_status "Using deployment bucket: $deployment_bucket"

    sam deploy \
        --template-file .aws-sam/build/template.yaml \
        --stack-name "$STACK_NAME" \
        --s3-bucket "$deployment_bucket" \
        --s3-prefix "$STACK_NAME" \
        --region "$REGION" \
        --capabilities CAPABILITY_IAM \
        --parameter-overrides Environment="$ENVIRONMENT" \
        --no-confirm-changeset \
        --no-fail-on-empty-changeset

    if [ $? -eq 0 ]; then
        print_success "SAM deployment completed"
    else
        print_error "SAM deployment failed"
        exit 1
    fi
}

# Get stack outputs
get_stack_outputs() {
    print_status "Retrieving stack outputs..."
    
    echo ""
    echo "📋 Stack Outputs:"
    echo "=================="
    
    aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$REGION" \
        --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
        --output table
    
    # Get API Gateway URL
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$REGION" \
        --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
        --output text)
    
    if [ -n "$API_URL" ]; then
        echo ""
        print_success "API Gateway URL: $API_URL"
        echo ""
        echo "🔗 Available Endpoints:"
        echo "======================="
        echo "Health Check:    ${API_URL}health"
        echo "Upload File:     ${API_URL}upload"
        echo "Analyze Resume:  ${API_URL}analyze"
        echo "Get Results:     ${API_URL}results/{analysis_id}"
        echo "List Results:    ${API_URL}results"
    fi
}

# Test deployment
test_deployment() {
    print_status "Testing deployment..."
    
    # Get API URL
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$REGION" \
        --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
        --output text)
    
    if [ -n "$API_URL" ]; then
        print_status "Testing health endpoint..."
        
        HEALTH_URL="${API_URL}health"
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL")
        
        if [ "$HTTP_STATUS" = "200" ]; then
            print_success "Health check passed (HTTP $HTTP_STATUS)"
        else
            print_warning "Health check returned HTTP $HTTP_STATUS"
        fi
    else
        print_warning "Could not retrieve API URL for testing"
    fi
}

# Enable Bedrock model access
enable_bedrock_access() {
    print_status "Checking Bedrock model access..."
    
    # Check if Claude models are available
    if aws bedrock list-foundation-models \
        --region "$REGION" \
        --by-provider anthropic \
        --query 'modelSummaries[?contains(modelId, `claude`)].modelId' \
        --output text &> /dev/null; then
        print_success "Bedrock Claude models are accessible"
    else
        print_warning "Bedrock Claude models may not be accessible"
        print_warning "You may need to request access in the AWS Bedrock console"
    fi
}

# Main deployment function
main() {
    echo "🎯 Resume Analyzer - AWS Lambda Hackathon Deployment"
    echo "===================================================="
    echo "Stack Name: $STACK_NAME"
    echo "Environment: $ENVIRONMENT"
    echo "Region: $REGION"
    echo ""
    
    # Change to backend directory
    cd "$(dirname "$0")"
    
    # Run deployment steps
    check_prerequisites
    DEPLOYMENT_BUCKET=$(create_deployment_bucket)
    validate_template
    build_application
    deploy_application "$DEPLOYMENT_BUCKET"
    enable_bedrock_access
    get_stack_outputs
    test_deployment
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📝 Next Steps:"
    echo "=============="
    echo "1. Update your frontend to use the API Gateway URL"
    echo "2. Test file upload and analysis functionality"
    echo "3. Monitor CloudWatch logs for any issues"
    echo "4. Consider setting up CloudWatch alarms for production"
    echo ""
    echo "🔧 Useful Commands:"
    echo "==================="
    echo "View logs:     sam logs --stack-name $STACK_NAME --tail"
    echo "Delete stack:  aws cloudformation delete-stack --stack-name $STACK_NAME"
    echo "Update stack:  ./deploy.sh"
    echo ""
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "delete")
        print_status "Deleting stack: $STACK_NAME"
        aws cloudformation delete-stack --stack-name "$STACK_NAME" --region "$REGION"
        print_success "Stack deletion initiated"
        ;;
    "status")
        aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region "$REGION"
        ;;
    "logs")
        sam logs --stack-name "$STACK_NAME" --tail
        ;;
    *)
        echo "Usage: $0 [deploy|delete|status|logs]"
        echo "  deploy (default): Deploy the application"
        echo "  delete: Delete the CloudFormation stack"
        echo "  status: Show stack status"
        echo "  logs: Tail CloudWatch logs"
        exit 1
        ;;
esac

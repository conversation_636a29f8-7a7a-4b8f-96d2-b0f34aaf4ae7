import boto3
import json
import logging
import os
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class AnalyticsHandler:
    """Handler for analytics and metrics using DynamoDB"""
    
    def __init__(self):
        self.region = os.getenv('AWS_REGION', 'us-east-2')
        self.table_name = os.getenv('ANALYTICS_TABLE_NAME', 'resume-analytics-dev')
        
        # Initialize DynamoDB client
        self.dynamodb = boto3.resource('dynamodb', region_name=self.region)
        self.table = self.dynamodb.Table(self.table_name)
    
    def log_analysis(self, resume_content: str, job_description: str, analysis_result: Dict[str, Any], user_id: str = None) -> str:
        """
        Log analysis result to DynamoDB for analytics
        """
        try:
            analysis_id = str(uuid.uuid4())
            timestamp = datetime.utcnow().isoformat()
            ttl = int(time.time()) + (30 * 24 * 60 * 60)  # 30 days TTL
            
            # Create analytics record
            item = {
                'analysis_id': analysis_id,
                'timestamp': timestamp,
                'user_id': user_id or 'anonymous',
                'match_percentage': analysis_result.get('match_percentage', 0),
                'strengths_count': len(analysis_result.get('strengths', [])),
                'missing_skills_count': len(analysis_result.get('missing_skills', [])),
                'recommendations_count': len(analysis_result.get('recommendations', [])),
                'resume_length': len(resume_content),
                'job_description_length': len(job_description),
                'ai_engine': 'gemini' if os.getenv('USE_GEMINI', 'true').lower() == 'true' else 'bedrock',
                'ttl': ttl,
                'metadata': {
                    'experience_meets_requirement': analysis_result.get('experience_analysis', {}).get('meets_requirement', False),
                    'education_meets_requirement': analysis_result.get('education_analysis', {}).get('meets_requirement', False),
                    'keyword_matches_count': len(analysis_result.get('keyword_matches', {}).get('matched', [])),
                    'keyword_missing_count': len(analysis_result.get('keyword_matches', {}).get('missing', []))
                }
            }
            
            # Save to DynamoDB
            self.table.put_item(Item=item)
            
            logger.info(f"Analytics logged for analysis_id: {analysis_id}")
            return analysis_id
            
        except Exception as e:
            logger.error(f"Failed to log analytics: {str(e)}")
            return None
    
    def get_analytics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get analytics summary for the last N hours
        """
        try:
            # Calculate time range
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=hours)
            
            # Query recent analyses
            response = self.table.scan(
                FilterExpression='#ts BETWEEN :start_time AND :end_time',
                ExpressionAttributeNames={
                    '#ts': 'timestamp'
                },
                ExpressionAttributeValues={
                    ':start_time': start_time.isoformat(),
                    ':end_time': end_time.isoformat()
                }
            )
            
            items = response.get('Items', [])
            
            if not items:
                return {
                    'total_analyses': 0,
                    'average_match_percentage': 0,
                    'time_range_hours': hours
                }
            
            # Calculate metrics
            total_analyses = len(items)
            match_percentages = [item.get('match_percentage', 0) for item in items]
            average_match = sum(match_percentages) / len(match_percentages) if match_percentages else 0
            
            # Count by AI engine
            gemini_count = sum(1 for item in items if item.get('ai_engine') == 'gemini')
            bedrock_count = sum(1 for item in items if item.get('ai_engine') == 'bedrock')
            
            # Experience/Education stats
            experience_met = sum(1 for item in items if item.get('metadata', {}).get('experience_meets_requirement', False))
            education_met = sum(1 for item in items if item.get('metadata', {}).get('education_meets_requirement', False))
            
            return {
                'total_analyses': total_analyses,
                'average_match_percentage': round(average_match, 2),
                'time_range_hours': hours,
                'ai_engine_usage': {
                    'gemini': gemini_count,
                    'bedrock': bedrock_count
                },
                'requirements_met': {
                    'experience': experience_met,
                    'education': education_met
                },
                'match_distribution': {
                    'excellent': sum(1 for p in match_percentages if p >= 80),
                    'good': sum(1 for p in match_percentages if 60 <= p < 80),
                    'fair': sum(1 for p in match_percentages if 40 <= p < 60),
                    'poor': sum(1 for p in match_percentages if p < 40)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get analytics summary: {str(e)}")
            return {
                'error': str(e),
                'total_analyses': 0,
                'time_range_hours': hours
            }
    
    def get_user_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get analysis history for a specific user
        """
        try:
            response = self.table.query(
                IndexName='UserAnalysisIndex',
                KeyConditionExpression='user_id = :user_id',
                ExpressionAttributeValues={
                    ':user_id': user_id
                },
                ScanIndexForward=False,  # Sort by timestamp descending
                Limit=limit
            )
            
            items = response.get('Items', [])
            
            # Format for frontend
            history = []
            for item in items:
                history.append({
                    'analysis_id': item.get('analysis_id'),
                    'timestamp': item.get('timestamp'),
                    'match_percentage': item.get('match_percentage'),
                    'strengths_count': item.get('strengths_count'),
                    'missing_skills_count': item.get('missing_skills_count'),
                    'ai_engine': item.get('ai_engine')
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Failed to get user history: {str(e)}")
            return []
    
    def get_trending_skills(self, days: int = 7) -> Dict[str, Any]:
        """
        Get trending missing skills from recent analyses
        """
        try:
            # This would require more complex querying in a real implementation
            # For now, return sample trending data
            return {
                'trending_missing_skills': [
                    {'skill': 'AWS', 'frequency': 45},
                    {'skill': 'Docker', 'frequency': 38},
                    {'skill': 'Kubernetes', 'frequency': 32},
                    {'skill': 'Machine Learning', 'frequency': 28},
                    {'skill': 'CI/CD', 'frequency': 25}
                ],
                'trending_strengths': [
                    {'skill': 'JavaScript', 'frequency': 67},
                    {'skill': 'Python', 'frequency': 58},
                    {'skill': 'React', 'frequency': 52},
                    {'skill': 'SQL', 'frequency': 48},
                    {'skill': 'Git', 'frequency': 44}
                ],
                'time_range_days': days
            }
            
        except Exception as e:
            logger.error(f"Failed to get trending skills: {str(e)}")
            return {
                'error': str(e),
                'time_range_days': days
            }
    
    def cleanup_old_records(self, days_old: int = 30) -> int:
        """
        Clean up old analytics records (called by scheduled Lambda)
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=days_old)
            
            # Scan for old records
            response = self.table.scan(
                FilterExpression='#ts < :cutoff_time',
                ExpressionAttributeNames={
                    '#ts': 'timestamp'
                },
                ExpressionAttributeValues={
                    ':cutoff_time': cutoff_time.isoformat()
                },
                ProjectionExpression='analysis_id, #ts'
            )
            
            items = response.get('Items', [])
            deleted_count = 0
            
            # Delete old records in batches
            with self.table.batch_writer() as batch:
                for item in items:
                    batch.delete_item(
                        Key={
                            'analysis_id': item['analysis_id'],
                            'timestamp': item['timestamp']
                        }
                    )
                    deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old analytics records")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old records: {str(e)}")
            return 0

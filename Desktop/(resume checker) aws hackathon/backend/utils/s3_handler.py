import boto3
import logging
import os
import uuid
from datetime import datetime
from typing import Optional

logger = logging.getLogger(__name__)

class S3Handler:
    """Handler for S3 operations"""
    
    def __init__(self):
        self.region = os.getenv('AWS_REGION', 'us-east-2')
        self.bucket_name = os.getenv('S3_BUCKET_NAME', 'resume-checker-uploads')
        
        # Initialize S3 client
        self.s3_client = boto3.client('s3', region_name=self.region)
    
    def save_file(self, file_bytes: bytes, file_type: str, user_id: Optional[str] = None) -> str:
        """
        Save file to S3 and return the key
        """
        try:
            # Generate unique file key
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            unique_id = str(uuid.uuid4())[:8]
            
            if user_id:
                file_key = f"resumes/{user_id}/{timestamp}_{unique_id}.{file_type}"
            else:
                file_key = f"resumes/anonymous/{timestamp}_{unique_id}.{file_type}"
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=file_key,
                Body=file_bytes,
                ContentType=self._get_content_type(file_type),
                ServerSideEncryption='AES256',  # Encrypt at rest
                Metadata={
                    'uploaded_at': timestamp,
                    'file_type': file_type,
                    'user_id': user_id or 'anonymous'
                }
            )
            
            logger.info(f"File saved to S3: {file_key}")
            return file_key
            
        except Exception as e:
            logger.error(f"Error saving file to S3: {str(e)}")
            raise Exception(f"Failed to save file: {str(e)}")
    
    def get_file(self, file_key: str) -> bytes:
        """
        Retrieve file from S3
        """
        try:
            response = self.s3_client.get_object(
                Bucket=self.bucket_name,
                Key=file_key
            )
            return response['Body'].read()
            
        except Exception as e:
            logger.error(f"Error retrieving file from S3: {str(e)}")
            raise Exception(f"Failed to retrieve file: {str(e)}")
    
    def delete_file(self, file_key: str) -> bool:
        """
        Delete file from S3
        """
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=file_key
            )
            logger.info(f"File deleted from S3: {file_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting file from S3: {str(e)}")
            return False
    
    def generate_presigned_url(self, file_key: str, expiration: int = 3600) -> str:
        """
        Generate a presigned URL for file access
        """
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': file_key},
                ExpiresIn=expiration
            )
            return url
            
        except Exception as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            raise Exception(f"Failed to generate presigned URL: {str(e)}")
    
    def _get_content_type(self, file_type: str) -> str:
        """
        Get appropriate content type for file
        """
        content_types = {
            'pdf': 'application/pdf',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'doc': 'application/msword',
            'txt': 'text/plain'
        }
        return content_types.get(file_type.lower(), 'application/octet-stream')
    
    def list_user_files(self, user_id: str, limit: int = 10) -> list:
        """
        List files for a specific user
        """
        try:
            prefix = f"resumes/{user_id}/"
            
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=limit
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat(),
                        'metadata': self._get_object_metadata(obj['Key'])
                    })
            
            return files
            
        except Exception as e:
            logger.error(f"Error listing user files: {str(e)}")
            return []
    
    def _get_object_metadata(self, file_key: str) -> dict:
        """
        Get metadata for an S3 object
        """
        try:
            response = self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=file_key
            )
            return response.get('Metadata', {})
            
        except Exception as e:
            logger.warning(f"Error getting metadata for {file_key}: {str(e)}")
            return {}
    
    def cleanup_old_files(self, days_old: int = 30) -> int:
        """
        Clean up files older than specified days
        """
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix="resumes/"
            )
            
            deleted_count = 0
            if 'Contents' in response:
                for obj in response['Contents']:
                    if obj['LastModified'].replace(tzinfo=None) < cutoff_date:
                        self.delete_file(obj['Key'])
                        deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old files")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
            return 0

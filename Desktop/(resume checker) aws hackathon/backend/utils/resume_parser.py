import io
import logging
from typing import Optional
import re

logger = logging.getLogger(__name__)

class ResumeParser:
    """Utility class for parsing resume files"""
    
    def __init__(self):
        pass
    
    def extract_text_from_pdf(self, pdf_bytes: bytes) -> str:
        """
        Extract text from PDF bytes using PyPDF2
        """
        try:
            # Import PyPDF2 (will be included in requirements.txt)
            import PyPDF2
            
            # Create a PDF reader object
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_bytes))
            
            # Extract text from all pages
            text_content = ""
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text_content += page.extract_text() + "\n"
            
            # Clean up the extracted text
            cleaned_text = self._clean_extracted_text(text_content)
            
            return cleaned_text
            
        except ImportError:
            logger.error("PyPDF2 not available. Install with: pip install PyPDF2")
            raise Exception("PDF processing not available. PyPDF2 library missing.")
        
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            raise Exception(f"Failed to extract text from PDF: {str(e)}")
    
    def extract_text_from_docx(self, docx_bytes: bytes) -> str:
        """
        Extract text from DOCX bytes using python-docx
        """
        try:
            # Import python-docx (will be included in requirements.txt)
            from docx import Document
            
            # Create a document object
            doc = Document(io.BytesIO(docx_bytes))
            
            # Extract text from all paragraphs
            text_content = ""
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            # Clean up the extracted text
            cleaned_text = self._clean_extracted_text(text_content)
            
            return cleaned_text
            
        except ImportError:
            logger.error("python-docx not available. Install with: pip install python-docx")
            raise Exception("DOCX processing not available. python-docx library missing.")
        
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {str(e)}")
            raise Exception(f"Failed to extract text from DOCX: {str(e)}")
    
    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean and normalize extracted text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters that might interfere with analysis
        text = re.sub(r'[^\w\s\-\.\,\(\)\@\#\+\/\:\;]', '', text)
        
        # Normalize line breaks
        text = text.replace('\n\n', '\n').replace('\r\n', '\n')
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def extract_contact_info(self, text: str) -> dict:
        """
        Extract basic contact information from resume text
        """
        contact_info = {
            'email': None,
            'phone': None,
            'linkedin': None
        }
        
        try:
            # Extract email
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            email_match = re.search(email_pattern, text)
            if email_match:
                contact_info['email'] = email_match.group()
            
            # Extract phone number (various formats)
            phone_patterns = [
                r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # ************ or ************ or 1234567890
                r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',   # (*************
                r'\+1[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}'  # ******-456-7890
            ]
            
            for pattern in phone_patterns:
                phone_match = re.search(pattern, text)
                if phone_match:
                    contact_info['phone'] = phone_match.group()
                    break
            
            # Extract LinkedIn profile
            linkedin_pattern = r'linkedin\.com/in/[\w\-]+'
            linkedin_match = re.search(linkedin_pattern, text, re.IGNORECASE)
            if linkedin_match:
                contact_info['linkedin'] = linkedin_match.group()
        
        except Exception as e:
            logger.warning(f"Error extracting contact info: {str(e)}")
        
        return contact_info
    
    def extract_skills_section(self, text: str) -> list:
        """
        Extract skills from resume text
        """
        skills = []
        
        try:
            # Look for skills section
            skills_patterns = [
                r'SKILLS?\s*:?\s*\n(.*?)(?=\n[A-Z]|\n\n|\Z)',
                r'TECHNICAL SKILLS?\s*:?\s*\n(.*?)(?=\n[A-Z]|\n\n|\Z)',
                r'CORE COMPETENCIES\s*:?\s*\n(.*?)(?=\n[A-Z]|\n\n|\Z)'
            ]
            
            for pattern in skills_patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
                if match:
                    skills_text = match.group(1)
                    # Split by common delimiters
                    skill_items = re.split(r'[,\n\|•·]', skills_text)
                    skills.extend([skill.strip() for skill in skill_items if skill.strip()])
                    break
        
        except Exception as e:
            logger.warning(f"Error extracting skills: {str(e)}")
        
        return skills
    
    def validate_resume_content(self, text: str) -> dict:
        """
        Validate that the text appears to be a resume
        """
        validation = {
            'is_valid': False,
            'confidence': 0,
            'issues': []
        }
        
        if not text or len(text.strip()) < 100:
            validation['issues'].append('Text too short to be a resume')
            return validation
        
        # Check for common resume indicators
        resume_indicators = [
            r'\b(experience|education|skills|work|employment)\b',
            r'\b(university|college|degree|bachelor|master|phd)\b',
            r'\b(company|corporation|inc|llc|ltd)\b',
            r'\b(manager|developer|engineer|analyst|coordinator)\b',
            r'\b(email|phone|address|linkedin)\b'
        ]
        
        matches = 0
        for pattern in resume_indicators:
            if re.search(pattern, text, re.IGNORECASE):
                matches += 1
        
        validation['confidence'] = min(matches * 20, 100)  # Max 100%
        validation['is_valid'] = validation['confidence'] >= 60
        
        if validation['confidence'] < 60:
            validation['issues'].append('Content does not appear to be a resume')
        
        return validation

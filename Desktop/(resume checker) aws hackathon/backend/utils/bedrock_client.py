import boto3
import json
import logging
from typing import Dict, Any
import os
import requests

logger = logging.getLogger(__name__)

class BedrockClient:
    """Client for interacting with Amazon Bedrock (<PERSON>) and Google Gemini"""

    def __init__(self):
        self.region = os.getenv('AWS_REGION', 'us-east-2')
        self.model_id = os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk')
        self.use_gemini = os.getenv('USE_GEMINI', 'true').lower() == 'true'

        # Initialize Bedrock client (fallback)
        try:
            self.bedrock_runtime = boto3.client(
                'bedrock-runtime',
                region_name=self.region
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Bedrock client: {e}")
            self.bedrock_runtime = None
    
    def analyze_resume_job_match(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze how well a resume matches a job description using <PERSON> or <PERSON>
        """
        try:
            # Try Gemini first if enabled
            if self.use_gemini and self.gemini_api_key:
                try:
                    return self._analyze_with_gemini(resume_content, job_description)
                except Exception as e:
                    logger.warning(f"Gemini analysis failed, falling back to Bedrock: {e}")

            # Fallback to Bedrock
            if self.bedrock_runtime:
                return self._analyze_with_bedrock(resume_content, job_description)
            else:
                # Return demo data if no AI service is available
                logger.warning("No AI service available, returning demo data")
                return self._get_demo_analysis()

        except Exception as e:
            logger.error(f"Error in analysis: {str(e)}")
            return self._get_demo_analysis()

    def _analyze_with_gemini(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """Analyze using Google Gemini API"""
        try:
            prompt = self._create_analysis_prompt(resume_content, job_description)

            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={self.gemini_api_key}"

            headers = {
                'Content-Type': 'application/json',
            }

            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 1,
                    "topP": 1,
                    "maxOutputTokens": 2048,
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()

            if 'candidates' in result and len(result['candidates']) > 0:
                analysis_text = result['candidates'][0]['content']['parts'][0]['text']
                return self._parse_analysis_response(analysis_text)
            else:
                raise Exception("No response from Gemini API")

        except Exception as e:
            logger.error(f"Gemini API error: {str(e)}")
            raise

    def _analyze_with_bedrock(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """Analyze using Amazon Bedrock (Claude)"""
        try:
            prompt = self._create_analysis_prompt(resume_content, job_description)

            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 2000,
                "temperature": 0.1,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            }

            response = self.bedrock_runtime.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body),
                contentType='application/json'
            )

            response_body = json.loads(response['body'].read())
            analysis_text = response_body['content'][0]['text']

            return self._parse_analysis_response(analysis_text)

        except Exception as e:
            logger.error(f"Bedrock API error: {str(e)}")
            raise
    
    def _create_analysis_prompt(self, resume_content: str, job_description: str) -> str:
        """Create a structured prompt for Claude analysis"""
        
        prompt = f"""You are an expert HR analyst and career counselor. Analyze how well the provided resume matches the given job description.

Please provide a comprehensive analysis in the following JSON format:

{{
    "match_percentage": <integer between 0-100>,
    "overall_assessment": "<brief summary of the match>",
    "strengths": [
        "<strength 1>",
        "<strength 2>",
        "<strength 3>"
    ],
    "missing_skills": [
        "<missing skill 1>",
        "<missing skill 2>",
        "<missing skill 3>"
    ],
    "recommendations": [
        "<recommendation 1>",
        "<recommendation 2>",
        "<recommendation 3>"
    ],
    "keyword_matches": {{
        "matched": ["<keyword1>", "<keyword2>"],
        "missing": ["<keyword3>", "<keyword4>"]
    }},
    "experience_analysis": {{
        "years_required": "<extracted from job description>",
        "years_candidate": "<estimated from resume>",
        "meets_requirement": <true/false>
    }},
    "education_analysis": {{
        "required": "<education requirement from job>",
        "candidate": "<candidate's education>",
        "meets_requirement": <true/false>
    }}
}}

RESUME:
{resume_content}

JOB DESCRIPTION:
{job_description}

Please analyze thoroughly and provide actionable insights. Focus on:
1. Technical skills alignment
2. Experience level match
3. Education requirements
4. Industry-specific keywords
5. Soft skills mentioned
6. Career progression relevance

Return only the JSON response, no additional text."""

        return prompt
    
    def _parse_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Claude's response into structured data"""
        try:
            # Try to extract JSON from the response
            # Claude sometimes adds extra text, so we need to find the JSON part
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                analysis = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['match_percentage', 'overall_assessment', 'strengths', 'missing_skills', 'recommendations']
                for field in required_fields:
                    if field not in analysis:
                        analysis[field] = self._get_default_value(field)
                
                return analysis
            else:
                # Fallback if JSON parsing fails
                return self._create_fallback_response(response_text)
                
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {str(e)}")
            return self._create_fallback_response(response_text)
    
    def _get_default_value(self, field: str) -> Any:
        """Get default values for missing fields"""
        defaults = {
            'match_percentage': 0,
            'overall_assessment': 'Analysis could not be completed',
            'strengths': [],
            'missing_skills': [],
            'recommendations': [],
            'keyword_matches': {'matched': [], 'missing': []},
            'experience_analysis': {'years_required': 'Unknown', 'years_candidate': 'Unknown', 'meets_requirement': False},
            'education_analysis': {'required': 'Unknown', 'candidate': 'Unknown', 'meets_requirement': False}
        }
        return defaults.get(field, None)
    
    def _create_fallback_response(self, raw_response: str) -> Dict[str, Any]:
        """Create a fallback response when JSON parsing fails"""
        return {
            'match_percentage': 50,
            'overall_assessment': 'Analysis completed but response format was unexpected',
            'strengths': ['Resume submitted successfully'],
            'missing_skills': ['Unable to determine specific missing skills'],
            'recommendations': ['Please try again or contact support'],
            'keyword_matches': {'matched': [], 'missing': []},
            'experience_analysis': {'years_required': 'Unknown', 'years_candidate': 'Unknown', 'meets_requirement': False},
            'education_analysis': {'required': 'Unknown', 'candidate': 'Unknown', 'meets_requirement': False},
            'raw_response': raw_response[:500]  # Include first 500 chars for debugging
        }

    def _get_demo_analysis(self) -> Dict[str, Any]:
        """Get demo analysis data when AI services are unavailable"""
        return {
            'match_percentage': 78,
            'overall_assessment': 'Your resume shows a strong match for this position with relevant experience and skills. There are several areas where you could strengthen your profile to become an even better candidate.',
            'strengths': [
                'Strong technical background in required programming languages',
                'Relevant work experience in similar industry',
                'Good educational background matching job requirements',
                'Demonstrated leadership and project management skills'
            ],
            'missing_skills': [
                'Cloud computing experience (AWS/Azure/GCP)',
                'Advanced data analysis and machine learning skills',
                'DevOps and CI/CD pipeline experience'
            ],
            'recommendations': [
                'Consider obtaining cloud certifications (AWS, Azure, or GCP)',
                'Highlight any project management experience more prominently',
                'Add specific examples of data analysis or ML projects',
                'Include metrics and quantifiable achievements in your experience'
            ],
            'keyword_matches': {
                'matched': ['JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'Git'],
                'missing': ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Machine Learning']
            },
            'experience_analysis': {
                'years_required': '3-5 years',
                'years_candidate': '4 years',
                'meets_requirement': True
            },
            'education_analysis': {
                'required': "Bachelor's degree in Computer Science or related field",
                'candidate': "Bachelor's in Computer Science",
                'meets_requirement': True
            }
        }

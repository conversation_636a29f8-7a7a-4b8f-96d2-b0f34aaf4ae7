import boto3
import json
import logging
from typing import Dict, Any
import os

logger = logging.getLogger(__name__)

class BedrockClient:
    """Client for interacting with Amazon Bedrock (Claude)"""
    
    def __init__(self):
        self.region = os.getenv('AWS_REGION', 'us-east-2')
        self.model_id = os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')
        
        # Initialize Bedrock client
        self.bedrock_runtime = boto3.client(
            'bedrock-runtime',
            region_name=self.region
        )
    
    def analyze_resume_job_match(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze how well a resume matches a job description using <PERSON>
        """
        try:
            # Create the prompt for Claude
            prompt = self._create_analysis_prompt(resume_content, job_description)
            
            # Prepare the request body
            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 2000,
                "temperature": 0.1,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            }
            
            # Call Bedrock
            response = self.bedrock_runtime.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body),
                contentType='application/json'
            )
            
            # Parse response
            response_body = json.loads(response['body'].read())
            analysis_text = response_body['content'][0]['text']
            
            # Parse the structured response
            analysis_result = self._parse_analysis_response(analysis_text)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error calling Bedrock: {str(e)}")
            raise Exception(f"Failed to analyze resume: {str(e)}")
    
    def _create_analysis_prompt(self, resume_content: str, job_description: str) -> str:
        """Create a structured prompt for Claude analysis"""
        
        prompt = f"""You are an expert HR analyst and career counselor. Analyze how well the provided resume matches the given job description.

Please provide a comprehensive analysis in the following JSON format:

{{
    "match_percentage": <integer between 0-100>,
    "overall_assessment": "<brief summary of the match>",
    "strengths": [
        "<strength 1>",
        "<strength 2>",
        "<strength 3>"
    ],
    "missing_skills": [
        "<missing skill 1>",
        "<missing skill 2>",
        "<missing skill 3>"
    ],
    "recommendations": [
        "<recommendation 1>",
        "<recommendation 2>",
        "<recommendation 3>"
    ],
    "keyword_matches": {{
        "matched": ["<keyword1>", "<keyword2>"],
        "missing": ["<keyword3>", "<keyword4>"]
    }},
    "experience_analysis": {{
        "years_required": "<extracted from job description>",
        "years_candidate": "<estimated from resume>",
        "meets_requirement": <true/false>
    }},
    "education_analysis": {{
        "required": "<education requirement from job>",
        "candidate": "<candidate's education>",
        "meets_requirement": <true/false>
    }}
}}

RESUME:
{resume_content}

JOB DESCRIPTION:
{job_description}

Please analyze thoroughly and provide actionable insights. Focus on:
1. Technical skills alignment
2. Experience level match
3. Education requirements
4. Industry-specific keywords
5. Soft skills mentioned
6. Career progression relevance

Return only the JSON response, no additional text."""

        return prompt
    
    def _parse_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Claude's response into structured data"""
        try:
            # Try to extract JSON from the response
            # Claude sometimes adds extra text, so we need to find the JSON part
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                analysis = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['match_percentage', 'overall_assessment', 'strengths', 'missing_skills', 'recommendations']
                for field in required_fields:
                    if field not in analysis:
                        analysis[field] = self._get_default_value(field)
                
                return analysis
            else:
                # Fallback if JSON parsing fails
                return self._create_fallback_response(response_text)
                
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {str(e)}")
            return self._create_fallback_response(response_text)
    
    def _get_default_value(self, field: str) -> Any:
        """Get default values for missing fields"""
        defaults = {
            'match_percentage': 0,
            'overall_assessment': 'Analysis could not be completed',
            'strengths': [],
            'missing_skills': [],
            'recommendations': [],
            'keyword_matches': {'matched': [], 'missing': []},
            'experience_analysis': {'years_required': 'Unknown', 'years_candidate': 'Unknown', 'meets_requirement': False},
            'education_analysis': {'required': 'Unknown', 'candidate': 'Unknown', 'meets_requirement': False}
        }
        return defaults.get(field, None)
    
    def _create_fallback_response(self, raw_response: str) -> Dict[str, Any]:
        """Create a fallback response when JSON parsing fails"""
        return {
            'match_percentage': 50,
            'overall_assessment': 'Analysis completed but response format was unexpected',
            'strengths': ['Resume submitted successfully'],
            'missing_skills': ['Unable to determine specific missing skills'],
            'recommendations': ['Please try again or contact support'],
            'keyword_matches': {'matched': [], 'missing': []},
            'experience_analysis': {'years_required': 'Unknown', 'years_candidate': 'Unknown', 'meets_requirement': False},
            'education_analysis': {'required': 'Unknown', 'candidate': 'Unknown', 'meets_requirement': False},
            'raw_response': raw_response[:500]  # Include first 500 chars for debugging
        }

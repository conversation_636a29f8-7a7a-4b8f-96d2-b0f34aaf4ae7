import boto3
import json
import logging
from typing import Dict, Any
import os
import requests

logger = logging.getLogger(__name__)

class BedrockClient:
    """Client for interacting with Amazon Bedrock (<PERSON>) and Google Gemini"""

    def __init__(self):
        self.region = os.getenv('AWS_REGION', 'us-east-2')
        self.model_id = os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBWbZPlEkbHV2etiqHQktiRprlQNuECPMk')
        self.use_gemini = os.getenv('USE_GEMINI', 'true').lower() == 'true'

        # Initialize Bedrock client (fallback)
        try:
            self.bedrock_runtime = boto3.client(
                'bedrock-runtime',
                region_name=self.region
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Bedrock client: {e}")
            self.bedrock_runtime = None
    
    def analyze_resume_job_match(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze how well a resume matches a job description using <PERSON> or <PERSON>
        """
        try:
            # Try Gemini first if enabled
            if self.use_gemini and self.gemini_api_key:
                try:
                    return self._analyze_with_gemini(resume_content, job_description)
                except Exception as e:
                    logger.warning(f"Gemini analysis failed, falling back to Bedrock: {e}")

            # Fallback to Bedrock
            if self.bedrock_runtime:
                return self._analyze_with_bedrock(resume_content, job_description)
            else:
                # Return demo data if no AI service is available
                logger.warning("No AI service available, returning demo data")
                return self._get_demo_analysis()

        except Exception as e:
            logger.error(f"Error in analysis: {str(e)}")
            return self._get_demo_analysis()

    def _analyze_with_gemini(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """Analyze using Google Gemini API"""
        try:
            prompt = self._create_analysis_prompt(resume_content, job_description)

            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={self.gemini_api_key}"

            headers = {
                'Content-Type': 'application/json',
            }

            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 1,
                    "topP": 1,
                    "maxOutputTokens": 2048,
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()

            if 'candidates' in result and len(result['candidates']) > 0:
                analysis_text = result['candidates'][0]['content']['parts'][0]['text']
                return self._parse_analysis_response(analysis_text)
            else:
                raise Exception("No response from Gemini API")

        except Exception as e:
            logger.error(f"Gemini API error: {str(e)}")
            raise

    def _analyze_with_bedrock(self, resume_content: str, job_description: str) -> Dict[str, Any]:
        """Analyze using Amazon Bedrock (Claude)"""
        try:
            prompt = self._create_analysis_prompt(resume_content, job_description)

            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 2000,
                "temperature": 0.1,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            }

            response = self.bedrock_runtime.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body),
                contentType='application/json'
            )

            response_body = json.loads(response['body'].read())
            analysis_text = response_body['content'][0]['text']

            return self._parse_analysis_response(analysis_text)

        except Exception as e:
            logger.error(f"Bedrock API error: {str(e)}")
            raise
    
    def _create_analysis_prompt(self, resume_content: str, job_description: str) -> str:
        """Create a structured prompt for Claude analysis"""
        
        prompt = f"""You are an expert HR analyst and career counselor with 15+ years of experience in talent acquisition across all industries. Analyze how well the provided resume matches the given job description with extreme attention to detail.

CRITICAL INSTRUCTIONS:

1. **FIELD MISMATCH DETECTION**: If the resume and job are from completely different fields (e.g., nurse resume for software job), set match_percentage to 0-15% and explain the mismatch clearly.

2. **DOCUMENT VALIDATION**: If either document is incomplete, corrupted, or not a proper resume/job description, indicate this in your analysis.

3. **COMPREHENSIVE SCORING**: Use the full 0-100% range:
   - 90-100%: Perfect match, ready to hire
   - 80-89%: Excellent match, minor gaps
   - 70-79%: Good match, some training needed
   - 60-69%: Moderate match, significant gaps
   - 40-59%: Poor match, major retraining required
   - 20-39%: Very poor match, wrong field/level
   - 0-19%: No match, completely different field

ANALYSIS EXAMPLES:

**Example 1 - Perfect Match (95%)**:
Resume: Senior Software Engineer, 8 years Python/React, AWS certified, led teams
Job: Senior Software Engineer, 5+ years Python/React, AWS experience, leadership
→ High match, exceeds requirements

**Example 2 - Field Mismatch (5%)**:
Resume: Registered Nurse, 10 years hospital experience, patient care
Job: Software Developer, Python programming, web development
→ Completely different field, no transferable technical skills

**Example 3 - Level Mismatch (25%)**:
Resume: Junior Developer, 1 year experience, basic HTML/CSS
Job: Senior Architect, 10+ years, system design, team leadership
→ Right field but massive experience gap

**Example 4 - Partial Match (65%)**:
Resume: Marketing Manager, 5 years B2B, some analytics tools
Job: Digital Marketing Specialist, 3+ years, analytics focus, B2B preferred
→ Good foundation but missing specific technical skills

DETAILED ANALYSIS REQUIREMENTS:

{{
    "match_percentage": <integer 0-100>,
    "field_compatibility": "<same_field|related_field|different_field>",
    "document_quality": {{
        "resume_valid": <true/false>,
        "job_description_valid": <true/false>,
        "issues": ["<any document issues>"]
    }},
    "overall_assessment": "<2-3 sentence summary with specific reasoning>",
    "strengths": ["<specific strength with evidence>", ...],
    "missing_skills": ["<specific missing skill>", ...],
    "recommendations": ["<actionable recommendation>", ...],
    "keyword_matches": {{
        "matched": ["<exact matched keywords>"],
        "missing": ["<critical missing keywords>"],
        "match_ratio": "<X/Y keywords matched>"
    }},
    "experience_analysis": {{
        "years_required": "<extracted requirement>",
        "years_candidate": "<calculated from resume>",
        "meets_requirement": <true/false>,
        "experience_gap": "<explanation if gap exists>"
    }},
    "education_analysis": {{
        "required": "<extracted requirement>",
        "candidate": "<extracted from resume>",
        "meets_requirement": <true/false>,
        "education_gap": "<explanation if gap exists>"
    }},
    "salary_expectation": {{
        "likely_range": "<estimated based on experience>",
        "job_level_match": "<junior|mid|senior|executive>"
    }},
    "red_flags": ["<any concerning gaps or issues>"],
    "competitive_advantages": ["<unique strengths that stand out>"]
}}

RESUME:
{resume_content}

JOB DESCRIPTION:
{job_description}

ANALYSIS FACTORS (Weight each appropriately):
1. **Technical Skills (30%)**: Exact matches, related skills, learning ability
2. **Experience Level (25%)**: Years, seniority, responsibility scope
3. **Industry Relevance (20%)**: Same/similar industry, domain knowledge
4. **Education (10%)**: Degree requirements, certifications, continuous learning
5. **Soft Skills (10%)**: Leadership, communication, teamwork evidence
6. **Cultural Fit (5%)**: Company values alignment, work style

EDGE CASES TO HANDLE:
- Career changers with transferable skills
- Recent graduates with relevant projects
- Overqualified candidates
- International candidates with different education systems
- Freelancers/contractors vs full-time experience
- Industry-specific certifications vs formal education
- Remote work experience vs on-site requirements

Return ONLY the JSON response, no additional text or formatting."""

        return prompt
    
    def _parse_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Claude's response into structured data"""
        try:
            # Try to extract JSON from the response
            # Claude sometimes adds extra text, so we need to find the JSON part
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                analysis = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['match_percentage', 'overall_assessment', 'strengths', 'missing_skills', 'recommendations', 'field_compatibility', 'document_quality']
                for field in required_fields:
                    if field not in analysis:
                        analysis[field] = self._get_default_value(field)
                
                return analysis
            else:
                # Fallback if JSON parsing fails
                return self._create_fallback_response(response_text)
                
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {str(e)}")
            return self._create_fallback_response(response_text)
    
    def _get_default_value(self, field: str) -> Any:
        """Get default values for missing fields"""
        defaults = {
            'match_percentage': 0,
            'field_compatibility': 'unknown',
            'document_quality': {'resume_valid': True, 'job_description_valid': True, 'issues': []},
            'overall_assessment': 'Analysis could not be completed',
            'strengths': [],
            'missing_skills': [],
            'recommendations': [],
            'keyword_matches': {'matched': [], 'missing': [], 'match_ratio': '0/0'},
            'experience_analysis': {'years_required': 'Unknown', 'years_candidate': 'Unknown', 'meets_requirement': False, 'experience_gap': 'Unable to determine'},
            'education_analysis': {'required': 'Unknown', 'candidate': 'Unknown', 'meets_requirement': False, 'education_gap': 'Unable to determine'},
            'salary_expectation': {'likely_range': 'Unable to estimate', 'job_level_match': 'unknown'},
            'red_flags': [],
            'competitive_advantages': []
        }
        return defaults.get(field, None)
    
    def _create_fallback_response(self, raw_response: str) -> Dict[str, Any]:
        """Create a fallback response when JSON parsing fails"""
        return {
            'match_percentage': 50,
            'field_compatibility': 'unknown',
            'document_quality': {'resume_valid': True, 'job_description_valid': True, 'issues': ['Response parsing failed']},
            'overall_assessment': 'Analysis completed but response format was unexpected. Please try again.',
            'strengths': ['Resume submitted successfully'],
            'missing_skills': ['Unable to determine specific missing skills'],
            'recommendations': ['Please try again or contact support'],
            'keyword_matches': {'matched': [], 'missing': [], 'match_ratio': '0/0'},
            'experience_analysis': {'years_required': 'Unknown', 'years_candidate': 'Unknown', 'meets_requirement': False, 'experience_gap': 'Unable to determine'},
            'education_analysis': {'required': 'Unknown', 'candidate': 'Unknown', 'meets_requirement': False, 'education_gap': 'Unable to determine'},
            'salary_expectation': {'likely_range': 'Unable to estimate', 'job_level_match': 'unknown'},
            'red_flags': ['Analysis parsing failed'],
            'competitive_advantages': [],
            'raw_response': raw_response[:500]  # Include first 500 chars for debugging
        }

    def _get_demo_analysis(self) -> Dict[str, Any]:
        """Get enhanced demo analysis data when AI services are unavailable"""
        return {
            'match_percentage': 78,
            'field_compatibility': 'same_field',
            'document_quality': {
                'resume_valid': True,
                'job_description_valid': True,
                'issues': []
            },
            'overall_assessment': 'Your resume shows a strong match for this position with relevant experience and skills. The candidate demonstrates solid technical competency and industry experience, though some advanced skills could be strengthened.',
            'strengths': [
                'Strong technical background in required programming languages (Python, JavaScript)',
                'Relevant 4+ years of work experience in similar industry and role',
                'Good educational background matching job requirements',
                'Demonstrated leadership and project management skills in previous roles',
                'Experience with modern development frameworks and methodologies'
            ],
            'missing_skills': [
                'Cloud computing experience (AWS/Azure/GCP) - critical for this role',
                'Advanced data analysis and machine learning skills',
                'DevOps and CI/CD pipeline experience',
                'Container technologies (Docker, Kubernetes)',
                'Microservices architecture experience'
            ],
            'recommendations': [
                'Obtain AWS certifications (Solutions Architect or Developer Associate) to meet cloud requirements',
                'Highlight any project management experience more prominently in resume summary',
                'Add specific examples of data analysis or ML projects, even if from personal learning',
                'Include metrics and quantifiable achievements (e.g., "improved performance by 30%")',
                'Consider taking online courses in DevOps tools to fill technical gaps'
            ],
            'keyword_matches': {
                'matched': ['JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'Git', 'Agile', 'REST API'],
                'missing': ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Machine Learning', 'Microservices'],
                'match_ratio': '8/14 keywords matched'
            },
            'experience_analysis': {
                'years_required': '3-5 years',
                'years_candidate': '4 years',
                'meets_requirement': True,
                'experience_gap': 'Experience level is appropriate, but lacks some senior-level responsibilities'
            },
            'education_analysis': {
                'required': "Bachelor's degree in Computer Science or related field",
                'candidate': "Bachelor's in Computer Science",
                'meets_requirement': True,
                'education_gap': 'Education requirement fully met'
            },
            'salary_expectation': {
                'likely_range': '$85,000 - $110,000',
                'job_level_match': 'mid'
            },
            'red_flags': [
                'Gap in cloud computing experience despite it being listed as required',
                'Limited mention of recent technology trends or continuous learning'
            ],
            'competitive_advantages': [
                'Strong foundation in core programming languages',
                'Proven track record in similar industry',
                'Leadership experience that could translate to senior roles',
                'Well-rounded technical skill set'
            ]
        }

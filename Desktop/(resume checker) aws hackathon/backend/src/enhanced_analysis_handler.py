"""
Enhanced Lambda function for comprehensive resume analysis
Integrates AWS Bedrock, Google Gemini, Textract, Comprehend, and A2I
"""
import json
import os
import logging
import requests
import boto3
from typing import Dict, Any, List
from utils import (
    create_response, 
    extract_text_from_file, 
    analyze_text_with_comprehend,
    analyze_with_bedrock,
    save_analysis_result,
    get_file_type
)

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

# AWS clients
textract_client = boto3.client('textract')
comprehend_client = boto3.client('comprehend')
s3_client = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')
sagemaker_client = boto3.client('sagemaker-a2i-runtime')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Enhanced resume analysis with multi-AI integration and line-by-line processing
    """
    try:
        logger.info("Starting enhanced resume analysis")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        # Extract parameters
        analysis_id = body.get('analysis_id')
        job_description = body.get('job_description')
        user_id = body.get('user_id', 'anonymous')
        enable_a2i_review = body.get('enable_a2i_review', False)
        
        if not analysis_id or not job_description:
            return create_response(400, {'error': 'Missing required parameters'})
        
        # Get file information and extract text
        file_info = get_file_information(analysis_id)
        if not file_info:
            return create_response(404, {'error': 'File not found for analysis'})
        
        # Enhanced text extraction with line-by-line processing
        resume_text = extract_enhanced_text(file_info)
        
        # Multi-AI analysis pipeline
        analysis_results = perform_comprehensive_analysis(
            resume_text, 
            job_description, 
            analysis_id,
            user_id
        )
        
        # Store results
        save_success = save_enhanced_analysis_result(analysis_id, analysis_results, user_id)
        
        # Trigger A2I review if requested and score is below threshold
        a2i_loop_name = None
        if enable_a2i_review and analysis_results.get('bedrock_analysis', {}).get('match_score', 0) < 70:
            a2i_loop_name = trigger_human_review(analysis_id, analysis_results)
        
        response_data = {
            'analysis_id': analysis_id,
            'status': 'completed',
            'results': analysis_results,
            'saved_to_database': save_success,
            'a2i_review_triggered': a2i_loop_name is not None,
            'a2i_loop_name': a2i_loop_name
        }
        
        logger.info(f"Enhanced analysis completed for ID: {analysis_id}")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Error in enhanced analysis: {str(e)}")
        return create_response(500, {
            'error': 'Analysis failed',
            'message': str(e),
            'analysis_id': body.get('analysis_id', 'unknown')
        })

def get_file_information(analysis_id: str) -> Dict[str, Any]:
    """Get file information from S3 and DynamoDB"""
    try:
        bucket_name = os.getenv('UPLOAD_BUCKET')
        
        # List objects with the analysis_id prefix
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=f"uploads/{analysis_id}/"
        )
        
        if 'Contents' not in response or len(response['Contents']) == 0:
            return None
        
        s3_key = response['Contents'][0]['Key']
        filename = s3_key.split('/')[-1]
        
        # Get file metadata
        file_metadata = s3_client.head_object(Bucket=bucket_name, Key=s3_key)
        
        return {
            'bucket': bucket_name,
            'key': s3_key,
            'filename': filename,
            'file_type': get_file_type(filename),
            'size': file_metadata['ContentLength'],
            'last_modified': file_metadata['LastModified']
        }
        
    except Exception as e:
        logger.error(f"Error getting file information: {str(e)}")
        return None

def extract_enhanced_text(file_info: Dict[str, Any]) -> str:
    """Enhanced text extraction with advanced Textract features"""
    try:
        bucket = file_info['bucket']
        key = file_info['key']
        file_type = file_info['file_type']
        
        logger.info(f"Extracting text from {file_type} file: {key}")
        
        if file_type.lower() in ['pdf', 'png', 'jpg', 'jpeg']:
            # Use advanced Textract analysis
            response = textract_client.analyze_document(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                },
                FeatureTypes=['TABLES', 'FORMS']
            )
            
            # Extract structured text with line-by-line processing
            text_blocks = []
            tables = []
            forms = []
            
            for block in response.get('Blocks', []):
                if block['BlockType'] == 'LINE':
                    text_blocks.append({
                        'text': block['Text'],
                        'confidence': block['Confidence'],
                        'geometry': block['Geometry']
                    })
                elif block['BlockType'] == 'TABLE':
                    tables.append(extract_table_data(block, response['Blocks']))
                elif block['BlockType'] == 'KEY_VALUE_SET':
                    forms.append(extract_form_data(block, response['Blocks']))
            
            # Combine all extracted text
            full_text = '\n'.join([block['text'] for block in text_blocks])
            
            return {
                'full_text': full_text,
                'structured_data': {
                    'lines': text_blocks,
                    'tables': tables,
                    'forms': forms
                }
            }
        
        else:
            # Handle other file types
            obj = s3_client.get_object(Bucket=bucket, Key=key)
            file_content = obj['Body'].read()
            
            if file_type.lower() == 'txt':
                text = file_content.decode('utf-8')
                return {
                    'full_text': text,
                    'structured_data': {
                        'lines': [{'text': line, 'confidence': 100} for line in text.split('\n')],
                        'tables': [],
                        'forms': []
                    }
                }
            else:
                return {
                    'full_text': "Unsupported file type for enhanced processing",
                    'structured_data': {'lines': [], 'tables': [], 'forms': []}
                }
                
    except Exception as e:
        logger.error(f"Error in enhanced text extraction: {str(e)}")
        raise

def extract_table_data(table_block: Dict, all_blocks: List[Dict]) -> Dict:
    """Extract structured table data from Textract response"""
    # Implementation for table extraction
    return {
        'type': 'table',
        'confidence': table_block.get('Confidence', 0),
        'data': 'Table data extraction implementation'
    }

def extract_form_data(form_block: Dict, all_blocks: List[Dict]) -> Dict:
    """Extract form field data from Textract response"""
    # Implementation for form extraction
    return {
        'type': 'form',
        'confidence': form_block.get('Confidence', 0),
        'data': 'Form data extraction implementation'
    }

def perform_comprehensive_analysis(resume_data: Dict, job_description: str, analysis_id: str, user_id: str) -> Dict[str, Any]:
    """Comprehensive analysis using multiple AI services"""
    try:
        resume_text = resume_data['full_text'] if isinstance(resume_data, dict) else resume_data
        
        # 1. AWS Comprehend Analysis
        comprehend_results = analyze_text_with_comprehend(resume_text)
        
        # 2. AWS Bedrock (Claude) Analysis
        bedrock_results = analyze_with_bedrock(resume_text, job_description)
        
        # 3. Google Gemini Analysis
        gemini_results = analyze_with_gemini(resume_text, job_description)
        
        # 4. Line-by-line detailed analysis
        detailed_analysis = perform_line_by_line_analysis(resume_data, job_description)
        
        # 5. Combine and synthesize results
        synthesized_results = synthesize_analysis_results(
            comprehend_results,
            bedrock_results,
            gemini_results,
            detailed_analysis
        )
        
        return {
            'analysis_id': analysis_id,
            'user_id': user_id,
            'timestamp': context.aws_request_id if 'context' in globals() else 'unknown',
            'comprehend_analysis': comprehend_results,
            'bedrock_analysis': bedrock_results,
            'gemini_analysis': gemini_results,
            'detailed_analysis': detailed_analysis,
            'synthesized_results': synthesized_results,
            'confidence_score': calculate_confidence_score(bedrock_results, gemini_results)
        }
        
    except Exception as e:
        logger.error(f"Error in comprehensive analysis: {str(e)}")
        raise

def analyze_with_gemini(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Analyze resume using Google Gemini API with line-by-line processing"""
    try:
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key or api_key == 'your-gemini-api-key-here':
            return {'error': 'Gemini API key not configured'}
        
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
        
        prompt = f"""
        Analyze this resume against the job description with extreme detail. Process line by line:

        RESUME:
        {resume_text}

        JOB DESCRIPTION:
        {job_description}

        Provide analysis in JSON format:
        {{
            "match_score": <0-100>,
            "line_by_line_analysis": [
                {{
                    "line_number": 1,
                    "content": "line content",
                    "relevance_score": <0-100>,
                    "job_relevance": "explanation",
                    "keywords_found": ["keyword1", "keyword2"],
                    "suggestions": "improvement suggestions"
                }}
            ],
            "skills_analysis": {{
                "found_skills": ["skill1", "skill2"],
                "missing_critical_skills": ["skill1", "skill2"],
                "skill_match_percentage": <0-100>
            }},
            "experience_analysis": {{
                "years_experience": <number>,
                "relevant_experience_percentage": <0-100>,
                "experience_gaps": ["gap1", "gap2"]
            }},
            "recommendations": [
                "specific recommendation 1",
                "specific recommendation 2"
            ],
            "overall_assessment": "detailed assessment"
        }}
        """
        
        payload = {
            "contents": [{
                "parts": [{"text": prompt}]
            }]
        }
        
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            text_response = result['candidates'][0]['content']['parts'][0]['text']
            
            # Parse JSON response
            try:
                return json.loads(text_response)
            except json.JSONDecodeError:
                return {
                    'error': 'Failed to parse Gemini response',
                    'raw_response': text_response
                }
        else:
            return {
                'error': f'Gemini API error: {response.status_code}',
                'message': response.text
            }
            
    except Exception as e:
        logger.error(f"Error with Gemini analysis: {str(e)}")
        return {'error': str(e)}

def perform_line_by_line_analysis(resume_data: Dict, job_description: str) -> Dict[str, Any]:
    """Detailed line-by-line analysis of resume content"""
    try:
        if isinstance(resume_data, dict) and 'structured_data' in resume_data:
            lines = resume_data['structured_data']['lines']
        else:
            # Fallback for simple text
            text = resume_data if isinstance(resume_data, str) else resume_data.get('full_text', '')
            lines = [{'text': line, 'confidence': 100} for line in text.split('\n')]
        
        job_keywords = extract_job_keywords(job_description)
        
        line_analysis = []
        for i, line_data in enumerate(lines):
            line_text = line_data['text'].strip()
            if not line_text:
                continue
                
            analysis = {
                'line_number': i + 1,
                'content': line_text,
                'confidence': line_data.get('confidence', 100),
                'keywords_found': find_keywords_in_line(line_text, job_keywords),
                'relevance_score': calculate_line_relevance(line_text, job_keywords),
                'category': categorize_line_content(line_text)
            }
            line_analysis.append(analysis)
        
        return {
            'total_lines': len(line_analysis),
            'relevant_lines': len([l for l in line_analysis if l['relevance_score'] > 50]),
            'line_details': line_analysis,
            'job_keywords': job_keywords
        }
        
    except Exception as e:
        logger.error(f"Error in line-by-line analysis: {str(e)}")
        return {'error': str(e)}

def extract_job_keywords(job_description: str) -> List[str]:
    """Extract important keywords from job description"""
    # Use Comprehend to extract key phrases
    try:
        response = comprehend_client.detect_key_phrases(
            Text=job_description[:5000],  # Limit text length
            LanguageCode='en'
        )
        
        keywords = [phrase['Text'].lower() for phrase in response['KeyPhrases']]
        
        # Add common technical terms and skills
        technical_terms = [
            'python', 'java', 'javascript', 'aws', 'docker', 'kubernetes',
            'machine learning', 'data science', 'sql', 'nosql', 'api',
            'microservices', 'agile', 'scrum', 'devops', 'ci/cd'
        ]
        
        # Filter job description for technical terms
        job_lower = job_description.lower()
        found_technical = [term for term in technical_terms if term in job_lower]
        
        return list(set(keywords + found_technical))
        
    except Exception as e:
        logger.error(f"Error extracting job keywords: {str(e)}")
        return []

def find_keywords_in_line(line_text: str, keywords: List[str]) -> List[str]:
    """Find job-relevant keywords in a resume line"""
    line_lower = line_text.lower()
    found = []
    
    for keyword in keywords:
        if keyword.lower() in line_lower:
            found.append(keyword)
    
    return found

def calculate_line_relevance(line_text: str, keywords: List[str]) -> int:
    """Calculate relevance score for a resume line"""
    if not line_text.strip():
        return 0
    
    found_keywords = find_keywords_in_line(line_text, keywords)
    
    # Base score on keyword density and line importance
    keyword_score = min(len(found_keywords) * 20, 80)
    
    # Boost for important sections
    line_lower = line_text.lower()
    if any(term in line_lower for term in ['experience', 'skills', 'education', 'projects']):
        keyword_score += 10
    
    return min(keyword_score, 100)

def categorize_line_content(line_text: str) -> str:
    """Categorize resume line content"""
    line_lower = line_text.lower()
    
    if any(term in line_lower for term in ['experience', 'work', 'employment', 'job']):
        return 'experience'
    elif any(term in line_lower for term in ['education', 'degree', 'university', 'college']):
        return 'education'
    elif any(term in line_lower for term in ['skills', 'technologies', 'programming']):
        return 'skills'
    elif any(term in line_lower for term in ['project', 'portfolio', 'github']):
        return 'projects'
    elif any(term in line_lower for term in ['contact', 'email', 'phone', 'address']):
        return 'contact'
    else:
        return 'other'

def synthesize_analysis_results(*analysis_results) -> Dict[str, Any]:
    """Combine and synthesize results from multiple AI analyses"""
    try:
        comprehend, bedrock, gemini, detailed = analysis_results
        
        # Calculate weighted average match score
        scores = []
        if bedrock.get('match_score'):
            scores.append(bedrock['match_score'])
        if gemini.get('match_score'):
            scores.append(gemini['match_score'])
        
        avg_score = sum(scores) / len(scores) if scores else 0
        
        # Combine recommendations
        all_recommendations = []
        if bedrock.get('recommendations'):
            all_recommendations.extend(bedrock['recommendations'])
        if gemini.get('recommendations'):
            all_recommendations.extend(gemini['recommendations'])
        
        return {
            'final_match_score': round(avg_score, 2),
            'confidence_level': 'high' if len(scores) >= 2 else 'medium',
            'combined_recommendations': list(set(all_recommendations)),
            'analysis_sources': len([r for r in analysis_results if r and not r.get('error')]),
            'detailed_insights': detailed
        }
        
    except Exception as e:
        logger.error(f"Error synthesizing results: {str(e)}")
        return {'error': str(e)}

def calculate_confidence_score(bedrock_results: Dict, gemini_results: Dict) -> float:
    """Calculate confidence score based on agreement between AI models"""
    try:
        bedrock_score = bedrock_results.get('match_score', 0)
        gemini_score = gemini_results.get('match_score', 0)
        
        if bedrock_score == 0 or gemini_score == 0:
            return 0.5  # Medium confidence with single source
        
        # Calculate agreement (inverse of difference)
        difference = abs(bedrock_score - gemini_score)
        agreement = max(0, 100 - difference) / 100
        
        return round(agreement, 2)
        
    except Exception as e:
        logger.error(f"Error calculating confidence: {str(e)}")
        return 0.0

def save_enhanced_analysis_result(analysis_id: str, results: Dict[str, Any], user_id: str) -> bool:
    """Save enhanced analysis results to DynamoDB"""
    try:
        table_name = os.getenv('RESULTS_TABLE')
        table = dynamodb.Table(table_name)
        
        from datetime import datetime, timedelta
        import uuid
        
        item = {
            'analysis_id': analysis_id,
            'user_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'ttl': int((datetime.utcnow() + timedelta(days=30)).timestamp()),
            'result': results,
            'status': 'completed',
            'match_score': results.get('synthesized_results', {}).get('final_match_score', 0),
            'job_category': 'general',  # Could be enhanced with job categorization
            'analysis_version': '2.0'
        }
        
        table.put_item(Item=item)
        logger.info(f"Saved enhanced analysis result for ID: {analysis_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error saving enhanced analysis result: {str(e)}")
        return False

def trigger_human_review(analysis_id: str, analysis_results: Dict[str, Any]) -> str:
    """Trigger A2I human review for low-confidence analyses"""
    try:
        flow_definition_arn = os.getenv('A2I_FLOW_ARN')
        if not flow_definition_arn:
            logger.warning("A2I Flow ARN not configured")
            return None
        
        loop_name = f"resume-review-{analysis_id}-{int(datetime.utcnow().timestamp())}"
        
        # Prepare input data for human reviewers
        human_loop_input = {
            'analysis_id': analysis_id,
            'match_score': analysis_results.get('synthesized_results', {}).get('final_match_score', 0),
            'overall_assessment': analysis_results.get('bedrock_analysis', {}).get('overall_assessment', ''),
            'skills': analysis_results.get('bedrock_analysis', {}).get('strengths', []),
            'recommendations': analysis_results.get('synthesized_results', {}).get('combined_recommendations', [])
        }
        
        response = sagemaker_client.start_human_loop(
            HumanLoopName=loop_name,
            FlowDefinitionArn=flow_definition_arn,
            HumanLoopInput={
                'InputContent': json.dumps(human_loop_input)
            }
        )
        
        logger.info(f"Started A2I human review loop: {loop_name}")
        return loop_name
        
    except Exception as e:
        logger.error(f"Error triggering human review: {str(e)}")
        return None

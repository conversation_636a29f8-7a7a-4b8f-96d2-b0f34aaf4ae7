"""
Lambda function for analyzing resumes against job descriptions
"""
import json
import os
import logging
from typing import Dict, Any
from utils import (
    create_response, 
    extract_text_from_file, 
    analyze_text_with_comprehend,
    analyze_with_bedrock,
    save_analysis_result,
    get_file_type
)

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Analyze resume against job description using AWS AI services
    
    Expected event structure:
    {
        "body": {
            "analysis_id": "uuid-string",
            "job_description": "job description text",
            "resume_text": "optional - if provided, skips file extraction"
        }
    }
    """
    try:
        logger.info("Processing analysis request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        # Validate required fields
        analysis_id = body.get('analysis_id')
        job_description = body.get('job_description')
        resume_text = body.get('resume_text')  # Optional - can be provided directly
        
        if not analysis_id:
            return create_response(400, {'error': 'Missing analysis_id'})
        
        if not job_description:
            return create_response(400, {'error': 'Missing job_description'})
        
        # Extract resume text if not provided
        if not resume_text:
            try:
                bucket_name = os.getenv('UPLOAD_BUCKET')
                
                # Find the uploaded file for this analysis_id
                import boto3
                s3_client = boto3.client('s3')
                
                # List objects with the analysis_id prefix
                response = s3_client.list_objects_v2(
                    Bucket=bucket_name,
                    Prefix=f"uploads/{analysis_id}/"
                )
                
                if 'Contents' not in response or len(response['Contents']) == 0:
                    return create_response(404, {
                        'error': 'No uploaded file found for this analysis_id'
                    })
                
                # Get the first (and should be only) file
                s3_key = response['Contents'][0]['Key']
                filename = s3_key.split('/')[-1]
                file_type = get_file_type(filename)
                
                # Extract text from the file
                resume_text = extract_text_from_file(bucket_name, s3_key, file_type)
                
                if not resume_text or len(resume_text.strip()) < 50:
                    return create_response(400, {
                        'error': 'Could not extract sufficient text from the uploaded file'
                    })
                
                logger.info(f"Extracted {len(resume_text)} characters from {filename}")
                
            except Exception as e:
                logger.error(f"Error extracting text from file: {str(e)}")
                return create_response(500, {
                    'error': 'Failed to extract text from uploaded file',
                    'message': str(e)
                })
        
        # Validate text lengths
        if len(resume_text.strip()) < 50:
            return create_response(400, {
                'error': 'Resume text is too short (minimum 50 characters)'
            })
        
        if len(job_description.strip()) < 50:
            return create_response(400, {
                'error': 'Job description is too short (minimum 50 characters)'
            })
        
        logger.info(f"Starting analysis for ID: {analysis_id}")
        logger.info(f"Resume text length: {len(resume_text)} characters")
        logger.info(f"Job description length: {len(job_description)} characters")
        
        # Perform analysis using AWS Comprehend
        comprehend_analysis = analyze_text_with_comprehend(resume_text)
        
        # Perform main analysis using AWS Bedrock (Claude)
        bedrock_analysis = analyze_with_bedrock(resume_text, job_description)
        
        # Combine results
        analysis_result = {
            'analysis_id': analysis_id,
            'timestamp': os.environ.get('AWS_LAMBDA_REQUEST_ID', 'unknown'),
            'resume_stats': {
                'character_count': len(resume_text),
                'word_count': len(resume_text.split()),
                'line_count': len(resume_text.split('\n'))
            },
            'job_description_stats': {
                'character_count': len(job_description),
                'word_count': len(job_description.split()),
                'line_count': len(job_description.split('\n'))
            },
            'comprehend_analysis': comprehend_analysis,
            'ai_analysis': bedrock_analysis,
            'processing_info': {
                'processed_at': context.aws_request_id if context else 'unknown',
                'function_version': context.function_version if context else 'unknown',
                'remaining_time_ms': context.get_remaining_time_in_millis() if context else 0
            }
        }
        
        # Save results to DynamoDB
        save_success = save_analysis_result(analysis_id, analysis_result)
        
        if not save_success:
            logger.warning("Failed to save analysis result to database")
        
        # Return the analysis result
        response_data = {
            'analysis_id': analysis_id,
            'status': 'completed',
            'result': analysis_result,
            'saved_to_database': save_success
        }
        
        logger.info(f"Analysis completed successfully for ID: {analysis_id}")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Unexpected error in analysis handler: {str(e)}")
        
        # Try to save error status to database
        try:
            error_result = {
                'analysis_id': body.get('analysis_id', 'unknown'),
                'status': 'error',
                'error': str(e),
                'timestamp': context.aws_request_id if context else 'unknown'
            }
            save_analysis_result(body.get('analysis_id', 'unknown'), error_result)
        except:
            pass  # Don't fail if we can't save error status
        
        return create_response(500, {
            'error': 'Internal server error',
            'message': 'An unexpected error occurred during analysis',
            'analysis_id': body.get('analysis_id', 'unknown')
        })

def validate_analysis_request(body: Dict[str, Any]) -> tuple[bool, str]:
    """Validate the analysis request body"""
    
    # Check required fields
    if 'analysis_id' not in body:
        return False, 'Missing analysis_id'
    
    if 'job_description' not in body:
        return False, 'Missing job_description'
    
    # Validate job description length
    job_desc = body['job_description'].strip()
    if len(job_desc) < 50:
        return False, 'Job description too short (minimum 50 characters)'
    
    if len(job_desc) > 10000:
        return False, 'Job description too long (maximum 10,000 characters)'
    
    # Validate resume text if provided
    if 'resume_text' in body:
        resume_text = body['resume_text'].strip()
        if len(resume_text) < 50:
            return False, 'Resume text too short (minimum 50 characters)'
        
        if len(resume_text) > 20000:
            return False, 'Resume text too long (maximum 20,000 characters)'
    
    return True, 'Valid'

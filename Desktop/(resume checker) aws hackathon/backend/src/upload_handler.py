"""
Lambda function for handling file uploads
"""
import json
import base64
import os
import logging
from typing import Dict, Any
import boto3
from botocore.exceptions import ClientError
from utils import create_response, generate_analysis_id, validate_file_type, get_file_type

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

s3_client = boto3.client('s3')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle file upload requests
    
    Expected event structure:
    {
        "body": {
            "filename": "resume.pdf",
            "content": "base64-encoded-file-content",
            "content_type": "application/pdf"
        }
    }
    """
    try:
        logger.info("Processing file upload request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        # Validate required fields
        required_fields = ['filename', 'content']
        for field in required_fields:
            if field not in body:
                return create_response(400, {'error': f'Missing required field: {field}'})
        
        filename = body['filename']
        file_content = body['content']
        content_type = body.get('content_type', 'application/octet-stream')
        
        # Validate file type
        if not validate_file_type(filename):
            return create_response(400, {
                'error': 'Unsupported file type. Supported types: PDF, DOCX, DOC, TXT, PNG, JPG, JPEG'
            })
        
        # Generate unique analysis ID and S3 key
        analysis_id = generate_analysis_id()
        file_extension = get_file_type(filename)
        s3_key = f"uploads/{analysis_id}/{filename}"
        
        # Decode base64 content
        try:
            file_data = base64.b64decode(file_content)
        except Exception as e:
            logger.error(f"Error decoding base64 content: {str(e)}")
            return create_response(400, {'error': 'Invalid base64 content'})
        
        # Validate file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        if len(file_data) > max_size:
            return create_response(400, {'error': 'File size exceeds 10MB limit'})
        
        # Upload to S3
        bucket_name = os.getenv('UPLOAD_BUCKET')
        if not bucket_name:
            logger.error("UPLOAD_BUCKET environment variable not set")
            return create_response(500, {'error': 'Server configuration error'})
        
        try:
            s3_client.put_object(
                Bucket=bucket_name,
                Key=s3_key,
                Body=file_data,
                ContentType=content_type,
                Metadata={
                    'analysis_id': analysis_id,
                    'original_filename': filename,
                    'file_type': file_extension
                }
            )
            
            logger.info(f"Successfully uploaded file to S3: {s3_key}")
            
        except ClientError as e:
            logger.error(f"Error uploading to S3: {str(e)}")
            return create_response(500, {'error': 'Failed to upload file'})
        
        # Return success response with analysis ID
        response_data = {
            'analysis_id': analysis_id,
            'filename': filename,
            'file_type': file_extension,
            's3_key': s3_key,
            'upload_status': 'success',
            'message': 'File uploaded successfully'
        }
        
        logger.info(f"File upload completed successfully. Analysis ID: {analysis_id}")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Unexpected error in upload handler: {str(e)}")
        return create_response(500, {
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing the upload'
        })

def generate_presigned_url(bucket: str, key: str, expiration: int = 3600) -> str:
    """Generate a presigned URL for S3 upload (alternative method)"""
    try:
        response = s3_client.generate_presigned_url(
            'put_object',
            Params={'Bucket': bucket, 'Key': key},
            ExpiresIn=expiration
        )
        return response
    except ClientError as e:
        logger.error(f"Error generating presigned URL: {str(e)}")
        raise

def handle_presigned_upload(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Alternative handler for generating presigned URLs for direct S3 upload
    This can be used for larger files to avoid Lambda payload limits
    """
    try:
        logger.info("Processing presigned URL request")
        
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        filename = body.get('filename')
        content_type = body.get('content_type', 'application/octet-stream')
        
        if not filename:
            return create_response(400, {'error': 'Missing filename'})
        
        # Validate file type
        if not validate_file_type(filename):
            return create_response(400, {
                'error': 'Unsupported file type. Supported types: PDF, DOCX, DOC, TXT, PNG, JPG, JPEG'
            })
        
        # Generate unique analysis ID and S3 key
        analysis_id = generate_analysis_id()
        s3_key = f"uploads/{analysis_id}/{filename}"
        
        # Generate presigned URL
        bucket_name = os.getenv('UPLOAD_BUCKET')
        presigned_url = generate_presigned_url(bucket_name, s3_key)
        
        response_data = {
            'analysis_id': analysis_id,
            'upload_url': presigned_url,
            's3_key': s3_key,
            'filename': filename,
            'expires_in': 3600  # 1 hour
        }
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Error generating presigned URL: {str(e)}")
        return create_response(500, {
            'error': 'Failed to generate upload URL',
            'message': str(e)
        })

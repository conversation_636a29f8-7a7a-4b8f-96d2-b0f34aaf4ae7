"""
Lambda function for AWS A2I (Augmented AI) human review integration
"""
import json
import os
import logging
import boto3
from datetime import datetime
from typing import Dict, Any
from utils import create_response

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

# AWS clients
sagemaker_a2i_client = boto3.client('sagemaker-a2i-runtime')
dynamodb = boto3.resource('dynamodb')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle A2I human review requests
    """
    try:
        logger.info("Processing A2I request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Get path and method
        path = event.get('path', '')
        method = event.get('httpMethod', 'GET')
        path_parameters = event.get('pathParameters') or {}
        
        # Route to appropriate handler
        if '/review/start' in path and method == 'POST':
            return handle_start_review(event)
        elif '/review' in path and method == 'GET' and path_parameters.get('loop_name'):
            return handle_get_review_status(event)
        else:
            return create_response(404, {'error': 'Endpoint not found'})
        
    except Exception as e:
        logger.error(f"Error in A2I handler: {str(e)}")
        return create_response(500, {
            'error': 'A2I service error',
            'message': str(e)
        })

def handle_start_review(event: Dict[str, Any]) -> Dict[str, Any]:
    """Start a human review loop for analysis results"""
    try:
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        # Validate required fields
        analysis_id = body.get('analysis_id')
        if not analysis_id:
            return create_response(400, {'error': 'Missing analysis_id'})
        
        # Get analysis results from DynamoDB
        results_table_name = os.getenv('RESULTS_TABLE')
        results_table = dynamodb.Table(results_table_name)
        
        response = results_table.get_item(Key={'analysis_id': analysis_id})
        
        if 'Item' not in response:
            return create_response(404, {'error': 'Analysis not found'})
        
        analysis_item = response['Item']
        analysis_results = analysis_item.get('result', {})
        
        # Check if review is warranted
        match_score = analysis_results.get('synthesized_results', {}).get('final_match_score', 0)
        confidence_score = analysis_results.get('confidence_score', 0)
        
        # Criteria for human review
        needs_review = (
            match_score < 70 or  # Low match score
            confidence_score < 0.8 or  # Low confidence
            body.get('force_review', False)  # Explicitly requested
        )
        
        if not needs_review:
            return create_response(200, {
                'review_needed': False,
                'reason': 'Analysis meets quality thresholds',
                'match_score': match_score,
                'confidence_score': confidence_score
            })
        
        # Create human loop
        flow_definition_arn = os.getenv('A2I_FLOW_ARN')
        if not flow_definition_arn:
            return create_response(500, {'error': 'A2I Flow Definition not configured'})
        
        loop_name = f"resume-review-{analysis_id}-{int(datetime.utcnow().timestamp())}"
        
        # Prepare input data for human reviewers
        human_loop_input = prepare_review_input(analysis_results, analysis_id)
        
        # Start the human loop
        try:
            response = sagemaker_a2i_client.start_human_loop(
                HumanLoopName=loop_name,
                FlowDefinitionArn=flow_definition_arn,
                HumanLoopInput={
                    'InputContent': json.dumps(human_loop_input)
                }
            )
            
            # Update analysis record with review status
            results_table.update_item(
                Key={'analysis_id': analysis_id},
                UpdateExpression='SET review_status = :status, review_loop_name = :loop_name, review_started_at = :timestamp',
                ExpressionAttributeValues={
                    ':status': 'in_review',
                    ':loop_name': loop_name,
                    ':timestamp': datetime.utcnow().isoformat()
                }
            )
            
            response_data = {
                'review_started': True,
                'loop_name': loop_name,
                'flow_definition_arn': flow_definition_arn,
                'analysis_id': analysis_id,
                'estimated_completion': '1-24 hours',
                'review_criteria': {
                    'match_score': match_score,
                    'confidence_score': confidence_score,
                    'needs_review_reason': get_review_reason(match_score, confidence_score)
                }
            }
            
            logger.info(f"Started human review loop: {loop_name}")
            return create_response(200, response_data)
            
        except Exception as e:
            logger.error(f"Error starting human loop: {str(e)}")
            return create_response(500, {
                'error': 'Failed to start human review',
                'message': str(e)
            })
        
    except Exception as e:
        logger.error(f"Error in start review: {str(e)}")
        return create_response(500, {
            'error': 'Failed to start review',
            'message': str(e)
        })

def handle_get_review_status(event: Dict[str, Any]) -> Dict[str, Any]:
    """Get the status of a human review loop"""
    try:
        loop_name = event['pathParameters']['loop_name']
        
        # Get loop status from A2I
        try:
            response = sagemaker_a2i_client.describe_human_loop(
                HumanLoopName=loop_name
            )
            
            loop_status = response['HumanLoopStatus']
            creation_time = response['CreationTime']
            
            response_data = {
                'loop_name': loop_name,
                'status': loop_status,
                'created_at': creation_time.isoformat(),
                'flow_definition_arn': response['FlowDefinitionArn']
            }
            
            # Add completion details if available
            if loop_status == 'Completed':
                response_data.update({
                    'completion_time': response.get('HumanLoopOutput', {}).get('OutputS3Uri'),
                    'output_location': response.get('HumanLoopOutput', {}).get('OutputS3Uri')
                })
                
                # Process the review results
                review_results = process_review_completion(loop_name, response)
                response_data['review_results'] = review_results
                
            elif loop_status == 'Failed':
                response_data['failure_reason'] = response.get('FailureReason', 'Unknown error')
            
            logger.info(f"Retrieved status for loop: {loop_name} - {loop_status}")
            return create_response(200, response_data)
            
        except sagemaker_a2i_client.exceptions.ResourceNotFound:
            return create_response(404, {'error': 'Human loop not found'})
        except Exception as e:
            logger.error(f"Error getting loop status: {str(e)}")
            return create_response(500, {
                'error': 'Failed to get review status',
                'message': str(e)
            })
        
    except Exception as e:
        logger.error(f"Error in get review status: {str(e)}")
        return create_response(500, {
            'error': 'Failed to get status',
            'message': str(e)
        })

def prepare_review_input(analysis_results: Dict[str, Any], analysis_id: str) -> Dict[str, Any]:
    """Prepare input data for human reviewers"""
    try:
        # Extract key information for review
        bedrock_analysis = analysis_results.get('bedrock_analysis', {})
        gemini_analysis = analysis_results.get('gemini_analysis', {})
        synthesized = analysis_results.get('synthesized_results', {})
        
        review_input = {
            'analysis_id': analysis_id,
            'match_score': synthesized.get('final_match_score', 0),
            'confidence_score': analysis_results.get('confidence_score', 0),
            'overall_assessment': bedrock_analysis.get('overall_assessment', ''),
            'strengths': bedrock_analysis.get('strengths', []),
            'missing_skills': bedrock_analysis.get('missing_skills', []),
            'recommendations': synthesized.get('combined_recommendations', []),
            'ai_sources': {
                'bedrock_score': bedrock_analysis.get('match_score', 0),
                'gemini_score': gemini_analysis.get('match_score', 0),
                'agreement_level': calculate_ai_agreement(bedrock_analysis, gemini_analysis)
            },
            'review_questions': [
                'Is the match score accurate based on the resume content?',
                'Are the identified skills correctly extracted?',
                'Are the missing skills relevant to the job?',
                'Are the recommendations helpful and actionable?',
                'Overall, is this analysis of good quality?'
            ]
        }
        
        return review_input
        
    except Exception as e:
        logger.error(f"Error preparing review input: {str(e)}")
        return {
            'analysis_id': analysis_id,
            'error': 'Failed to prepare review data'
        }

def calculate_ai_agreement(bedrock_results: Dict, gemini_results: Dict) -> str:
    """Calculate agreement level between AI models"""
    try:
        bedrock_score = bedrock_results.get('match_score', 0)
        gemini_score = gemini_results.get('match_score', 0)
        
        if bedrock_score == 0 or gemini_score == 0:
            return 'insufficient_data'
        
        difference = abs(bedrock_score - gemini_score)
        
        if difference <= 10:
            return 'high_agreement'
        elif difference <= 20:
            return 'moderate_agreement'
        else:
            return 'low_agreement'
            
    except Exception:
        return 'unknown'

def get_review_reason(match_score: float, confidence_score: float) -> str:
    """Get human-readable reason for review"""
    reasons = []
    
    if match_score < 50:
        reasons.append('Very low match score')
    elif match_score < 70:
        reasons.append('Low match score')
    
    if confidence_score < 0.6:
        reasons.append('Low AI confidence')
    elif confidence_score < 0.8:
        reasons.append('Moderate AI confidence')
    
    return '; '.join(reasons) if reasons else 'Quality assurance review'

def process_review_completion(loop_name: str, loop_response: Dict) -> Dict[str, Any]:
    """Process completed human review results"""
    try:
        # In a real implementation, you would:
        # 1. Download the review results from S3
        # 2. Parse the human reviewer's feedback
        # 3. Update the original analysis with human insights
        # 4. Potentially trigger retraining or model updates
        
        output_s3_uri = loop_response.get('HumanLoopOutput', {}).get('OutputS3Uri')
        
        if output_s3_uri:
            # Parse S3 URI and download results
            # s3://bucket/path/to/results.json
            logger.info(f"Processing review results from: {output_s3_uri}")
            
            # For now, return placeholder data
            return {
                'review_completed': True,
                'output_location': output_s3_uri,
                'processing_status': 'completed',
                'next_steps': 'Results will be integrated into the analysis'
            }
        else:
            return {
                'review_completed': True,
                'output_location': None,
                'processing_status': 'no_output',
                'next_steps': 'Manual review required'
            }
        
    except Exception as e:
        logger.error(f"Error processing review completion: {str(e)}")
        return {
            'review_completed': True,
            'processing_status': 'error',
            'error': str(e)
        }

def list_user_reviews(user_id: str) -> List[Dict[str, Any]]:
    """List all reviews for a user"""
    try:
        results_table_name = os.getenv('RESULTS_TABLE')
        results_table = dynamodb.Table(results_table_name)
        
        # Query for user's analyses that have reviews
        response = results_table.query(
            IndexName='UserAnalysisIndex',
            KeyConditionExpression='user_id = :user_id',
            FilterExpression='attribute_exists(review_loop_name)',
            ExpressionAttributeValues={':user_id': user_id}
        )
        
        reviews = []
        for item in response.get('Items', []):
            review_info = {
                'analysis_id': item['analysis_id'],
                'loop_name': item.get('review_loop_name'),
                'review_status': item.get('review_status'),
                'started_at': item.get('review_started_at'),
                'match_score': item.get('match_score', 0)
            }
            reviews.append(review_info)
        
        return reviews
        
    except Exception as e:
        logger.error(f"Error listing user reviews: {str(e)}")
        return []

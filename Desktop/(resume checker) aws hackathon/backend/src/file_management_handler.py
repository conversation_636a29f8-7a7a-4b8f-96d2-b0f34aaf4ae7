"""
Lambda function for enhanced file management with S3 and DynamoDB integration
"""
import json
import os
import logging
import boto3
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List
from utils import create_response, validate_file_type, get_file_type

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

# AWS clients
s3_client = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle file management requests
    """
    try:
        logger.info("Processing file management request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Get path and method
        path = event.get('path', '')
        method = event.get('httpMethod', 'GET')
        path_parameters = event.get('pathParameters') or {}
        
        # Route to appropriate handler
        if '/files' in path and method == 'GET' and not path_parameters.get('file_id'):
            return handle_list_files(event)
        elif '/files' in path and method == 'GET' and path_parameters.get('file_id'):
            return handle_get_file(event)
        elif '/files' in path and method == 'DELETE' and path_parameters.get('file_id'):
            return handle_delete_file(event)
        else:
            return create_response(404, {'error': 'Endpoint not found'})
        
    except Exception as e:
        logger.error(f"Error in file management handler: {str(e)}")
        return create_response(500, {
            'error': 'File management service error',
            'message': str(e)
        })

def get_user_from_token(event: Dict[str, Any]) -> Dict[str, Any]:
    """Extract and validate user from authorization token"""
    try:
        headers = event.get('headers', {})
        auth_header = headers.get('Authorization') or headers.get('authorization')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            return {'valid': False, 'error': 'Missing authorization header'}
        
        access_token = auth_header.replace('Bearer ', '')
        
        # Validate token with Cognito
        cognito_client = boto3.client('cognito-idp')
        user_info = cognito_client.get_user(AccessToken=access_token)
        user_attributes = {attr['Name']: attr['Value'] for attr in user_info['UserAttributes']}
        
        return {
            'valid': True,
            'user_id': user_info['Username'],
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name')
        }
        
    except Exception as e:
        logger.error(f"Token validation failed: {str(e)}")
        return {'valid': False, 'error': 'Invalid token'}

def handle_list_files(event: Dict[str, Any]) -> Dict[str, Any]:
    """List user's uploaded files"""
    try:
        # Validate user authentication
        user_info = get_user_from_token(event)
        if not user_info['valid']:
            return create_response(401, {'error': user_info['error']})
        
        user_id = user_info['user_id']
        
        # Get query parameters
        query_params = event.get('queryStringParameters') or {}
        limit = int(query_params.get('limit', '20'))
        file_type = query_params.get('file_type')
        
        # Query DynamoDB for user's files
        files_table_name = os.getenv('FILES_TABLE')
        files_table = dynamodb.Table(files_table_name)
        
        # Query using UserFilesIndex
        query_params = {
            'IndexName': 'UserFilesIndex',
            'KeyConditionExpression': 'user_id = :user_id',
            'ExpressionAttributeValues': {':user_id': user_id},
            'ScanIndexForward': False,  # Most recent first
            'Limit': limit
        }
        
        # Add file type filter if specified
        if file_type:
            query_params['FilterExpression'] = 'file_type = :file_type'
            query_params['ExpressionAttributeValues'][':file_type'] = file_type
        
        response = files_table.query(**query_params)
        files = response.get('Items', [])
        
        # Enrich with S3 information
        enriched_files = []
        bucket_name = os.getenv('UPLOAD_BUCKET')
        
        for file_item in files:
            try:
                # Get S3 object metadata
                s3_response = s3_client.head_object(
                    Bucket=bucket_name,
                    Key=file_item['s3_key']
                )
                
                file_info = {
                    'file_id': file_item['file_id'],
                    'filename': file_item['filename'],
                    'file_type': file_item['file_type'],
                    'size': s3_response['ContentLength'],
                    'uploaded_at': file_item['uploaded_at'],
                    'last_modified': s3_response['LastModified'].isoformat(),
                    'analysis_count': file_item.get('analysis_count', 0),
                    'tags': file_item.get('tags', []),
                    'status': 'available'
                }
                
                enriched_files.append(file_info)
                
            except s3_client.exceptions.NoSuchKey:
                # File exists in DynamoDB but not in S3
                file_info = dict(file_item)
                file_info['status'] = 'missing'
                enriched_files.append(file_info)
            except Exception as e:
                logger.warning(f"Error getting S3 info for file {file_item['file_id']}: {str(e)}")
                file_info = dict(file_item)
                file_info['status'] = 'error'
                enriched_files.append(file_info)
        
        response_data = {
            'files': enriched_files,
            'total_count': len(enriched_files),
            'user_id': user_id,
            'has_more': 'LastEvaluatedKey' in response
        }
        
        logger.info(f"Listed {len(enriched_files)} files for user: {user_id}")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        return create_response(500, {
            'error': 'Failed to list files',
            'message': str(e)
        })

def handle_get_file(event: Dict[str, Any]) -> Dict[str, Any]:
    """Get specific file information and generate download URL"""
    try:
        # Validate user authentication
        user_info = get_user_from_token(event)
        if not user_info['valid']:
            return create_response(401, {'error': user_info['error']})
        
        user_id = user_info['user_id']
        file_id = event['pathParameters']['file_id']
        
        # Get file metadata from DynamoDB
        files_table_name = os.getenv('FILES_TABLE')
        files_table = dynamodb.Table(files_table_name)
        
        response = files_table.get_item(Key={'file_id': file_id})
        
        if 'Item' not in response:
            return create_response(404, {'error': 'File not found'})
        
        file_item = response['Item']
        
        # Verify file belongs to user
        if file_item['user_id'] != user_id:
            return create_response(403, {'error': 'Access denied'})
        
        # Generate presigned URL for download
        bucket_name = os.getenv('UPLOAD_BUCKET')
        
        try:
            download_url = s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': file_item['s3_key']},
                ExpiresIn=3600  # 1 hour
            )
            
            # Get S3 object metadata
            s3_response = s3_client.head_object(
                Bucket=bucket_name,
                Key=file_item['s3_key']
            )
            
            file_info = {
                'file_id': file_item['file_id'],
                'filename': file_item['filename'],
                'file_type': file_item['file_type'],
                'size': s3_response['ContentLength'],
                'uploaded_at': file_item['uploaded_at'],
                'last_modified': s3_response['LastModified'].isoformat(),
                'download_url': download_url,
                'expires_in': 3600,
                'analysis_count': file_item.get('analysis_count', 0),
                'tags': file_item.get('tags', []),
                'metadata': file_item.get('metadata', {}),
                'status': 'available'
            }
            
        except s3_client.exceptions.NoSuchKey:
            return create_response(404, {'error': 'File not found in storage'})
        
        logger.info(f"Generated download URL for file: {file_id}")
        return create_response(200, file_info)
        
    except Exception as e:
        logger.error(f"Error getting file: {str(e)}")
        return create_response(500, {
            'error': 'Failed to get file',
            'message': str(e)
        })

def handle_delete_file(event: Dict[str, Any]) -> Dict[str, Any]:
    """Delete file from S3 and DynamoDB"""
    try:
        # Validate user authentication
        user_info = get_user_from_token(event)
        if not user_info['valid']:
            return create_response(401, {'error': user_info['error']})
        
        user_id = user_info['user_id']
        file_id = event['pathParameters']['file_id']
        
        # Get file metadata from DynamoDB
        files_table_name = os.getenv('FILES_TABLE')
        files_table = dynamodb.Table(files_table_name)
        
        response = files_table.get_item(Key={'file_id': file_id})
        
        if 'Item' not in response:
            return create_response(404, {'error': 'File not found'})
        
        file_item = response['Item']
        
        # Verify file belongs to user
        if file_item['user_id'] != user_id:
            return create_response(403, {'error': 'Access denied'})
        
        bucket_name = os.getenv('UPLOAD_BUCKET')
        
        # Delete from S3
        try:
            s3_client.delete_object(
                Bucket=bucket_name,
                Key=file_item['s3_key']
            )
            logger.info(f"Deleted S3 object: {file_item['s3_key']}")
        except Exception as e:
            logger.warning(f"Failed to delete S3 object: {str(e)}")
        
        # Delete from DynamoDB
        files_table.delete_item(Key={'file_id': file_id})
        
        # Also delete related analysis results
        try:
            results_table_name = os.getenv('RESULTS_TABLE')
            if results_table_name:
                results_table = dynamodb.Table(results_table_name)
                
                # Query for analyses related to this file
                # This would require additional GSI on file_id if implemented
                logger.info("Analysis cleanup would be implemented here")
                
        except Exception as e:
            logger.warning(f"Failed to cleanup analysis results: {str(e)}")
        
        response_data = {
            'file_id': file_id,
            'filename': file_item['filename'],
            'message': 'File deleted successfully'
        }
        
        logger.info(f"Deleted file: {file_id} for user: {user_id}")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        return create_response(500, {
            'error': 'Failed to delete file',
            'message': str(e)
        })

def save_file_metadata(file_id: str, user_id: str, filename: str, s3_key: str, 
                      file_size: int, metadata: Dict[str, Any] = None) -> bool:
    """Save file metadata to DynamoDB"""
    try:
        files_table_name = os.getenv('FILES_TABLE')
        files_table = dynamodb.Table(files_table_name)
        
        file_item = {
            'file_id': file_id,
            'user_id': user_id,
            'filename': filename,
            'file_type': get_file_type(filename),
            's3_key': s3_key,
            'file_size': file_size,
            'uploaded_at': datetime.utcnow().isoformat(),
            'ttl': int((datetime.utcnow() + timedelta(days=90)).timestamp()),
            'analysis_count': 0,
            'tags': [],
            'metadata': metadata or {},
            'status': 'uploaded'
        }
        
        files_table.put_item(Item=file_item)
        logger.info(f"Saved file metadata: {file_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error saving file metadata: {str(e)}")
        return False

def get_user_file_stats(user_id: str) -> Dict[str, Any]:
    """Get file statistics for a user"""
    try:
        files_table_name = os.getenv('FILES_TABLE')
        files_table = dynamodb.Table(files_table_name)
        
        # Query user's files
        response = files_table.query(
            IndexName='UserFilesIndex',
            KeyConditionExpression='user_id = :user_id',
            ExpressionAttributeValues={':user_id': user_id}
        )
        
        files = response.get('Items', [])
        
        # Calculate statistics
        total_files = len(files)
        total_size = sum(file_item.get('file_size', 0) for file_item in files)
        file_types = {}
        
        for file_item in files:
            file_type = file_item.get('file_type', 'unknown')
            file_types[file_type] = file_types.get(file_type, 0) + 1
        
        return {
            'total_files': total_files,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'file_types': file_types,
            'most_recent': files[0]['uploaded_at'] if files else None
        }
        
    except Exception as e:
        logger.error(f"Error getting file stats: {str(e)}")
        return {
            'total_files': 0,
            'total_size_bytes': 0,
            'total_size_mb': 0,
            'file_types': {},
            'most_recent': None
        }

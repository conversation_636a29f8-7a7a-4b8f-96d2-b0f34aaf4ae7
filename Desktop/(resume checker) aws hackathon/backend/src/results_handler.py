"""
Lambda function for retrieving analysis results
"""
import json
import os
import logging
from typing import Dict, Any, Optional
import boto3
from botocore.exceptions import ClientError
from utils import create_response, get_analysis_result

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

dynamodb = boto3.resource('dynamodb')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle requests for analysis results
    
    Supports:
    - GET /results/{analysis_id} - Get specific analysis result
    - GET /results - List recent analysis results
    """
    try:
        logger.info("Processing results request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        http_method = event.get('httpMethod', 'GET')
        path_parameters = event.get('pathParameters') or {}
        query_parameters = event.get('queryStringParameters') or {}
        
        if http_method == 'GET':
            # Check if this is a request for a specific analysis
            analysis_id = path_parameters.get('analysis_id')
            
            if analysis_id:
                return get_specific_result(analysis_id)
            else:
                return list_recent_results(query_parameters)
        
        else:
            return create_response(405, {'error': 'Method not allowed'})
        
    except Exception as e:
        logger.error(f"Unexpected error in results handler: {str(e)}")
        return create_response(500, {
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while retrieving results'
        })

def get_specific_result(analysis_id: str) -> Dict[str, Any]:
    """Get a specific analysis result by ID"""
    try:
        logger.info(f"Retrieving result for analysis ID: {analysis_id}")
        
        # Validate analysis_id format (basic UUID check)
        if not analysis_id or len(analysis_id) < 10:
            return create_response(400, {'error': 'Invalid analysis_id format'})
        
        # Get result from DynamoDB
        result = get_analysis_result(analysis_id)
        
        if result is None:
            return create_response(404, {
                'error': 'Analysis result not found',
                'analysis_id': analysis_id,
                'message': 'The requested analysis result does not exist or has expired'
            })
        
        # Remove sensitive information if needed
        cleaned_result = clean_result_for_response(result)
        
        response_data = {
            'analysis_id': analysis_id,
            'status': 'found',
            'result': cleaned_result
        }
        
        logger.info(f"Successfully retrieved result for analysis ID: {analysis_id}")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Error retrieving specific result: {str(e)}")
        return create_response(500, {
            'error': 'Failed to retrieve analysis result',
            'analysis_id': analysis_id,
            'message': str(e)
        })

def list_recent_results(query_params: Dict[str, str]) -> Dict[str, Any]:
    """List recent analysis results with pagination"""
    try:
        logger.info("Retrieving list of recent results")
        
        # Parse query parameters
        limit = int(query_params.get('limit', '10'))
        last_key = query_params.get('last_key')
        
        # Validate limit
        if limit < 1 or limit > 100:
            limit = 10
        
        table_name = os.getenv('RESULTS_TABLE')
        table = dynamodb.Table(table_name)
        
        # Query parameters for DynamoDB scan
        scan_params = {
            'Limit': limit,
            'ProjectionExpression': 'analysis_id, created_at, #status, #result.ai_analysis.match_score, #result.ai_analysis.overall_assessment',
            'ExpressionAttributeNames': {
                '#status': 'status',
                '#result': 'result'
            }
        }
        
        # Add pagination if last_key is provided
        if last_key:
            try:
                # Decode the last_key (in real implementation, you'd want to encrypt/sign this)
                import base64
                decoded_key = json.loads(base64.b64decode(last_key).decode('utf-8'))
                scan_params['ExclusiveStartKey'] = decoded_key
            except Exception as e:
                logger.warning(f"Invalid last_key parameter: {str(e)}")
                # Continue without pagination
        
        # Perform the scan
        response = table.scan(**scan_params)
        
        items = response.get('Items', [])
        
        # Sort by created_at (most recent first)
        items.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Prepare response
        response_data = {
            'results': items,
            'count': len(items),
            'limit': limit
        }
        
        # Add pagination info if there are more items
        if 'LastEvaluatedKey' in response:
            # Encode the last key for next request
            import base64
            encoded_key = base64.b64encode(
                json.dumps(response['LastEvaluatedKey'], default=str).encode('utf-8')
            ).decode('utf-8')
            response_data['next_page_key'] = encoded_key
            response_data['has_more'] = True
        else:
            response_data['has_more'] = False
        
        logger.info(f"Retrieved {len(items)} results")
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error(f"Error listing recent results: {str(e)}")
        return create_response(500, {
            'error': 'Failed to retrieve results list',
            'message': str(e)
        })

def clean_result_for_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """Clean and format result for API response"""
    try:
        # Create a copy to avoid modifying the original
        cleaned = dict(result)
        
        # Remove or mask sensitive information
        if 'ttl' in cleaned:
            del cleaned['ttl']
        
        # Ensure consistent structure
        if 'result' in cleaned and isinstance(cleaned['result'], dict):
            result_data = cleaned['result']
            
            # Ensure all expected fields are present with defaults
            if 'ai_analysis' in result_data:
                ai_analysis = result_data['ai_analysis']
                
                # Set defaults for missing fields
                ai_analysis.setdefault('match_score', 0)
                ai_analysis.setdefault('overall_assessment', 'No assessment available')
                ai_analysis.setdefault('strengths', [])
                ai_analysis.setdefault('missing_skills', [])
                ai_analysis.setdefault('recommendations', [])
                ai_analysis.setdefault('skill_matches', [])
                ai_analysis.setdefault('experience_analysis', {})
                ai_analysis.setdefault('education_analysis', {})
                ai_analysis.setdefault('improvement_areas', [])
        
        return cleaned
        
    except Exception as e:
        logger.warning(f"Error cleaning result: {str(e)}")
        return result  # Return original if cleaning fails

def get_analysis_statistics() -> Dict[str, Any]:
    """Get overall statistics about analyses (bonus endpoint)"""
    try:
        table_name = os.getenv('RESULTS_TABLE')
        table = dynamodb.Table(table_name)
        
        # Scan to get basic statistics
        response = table.scan(
            ProjectionExpression='analysis_id, created_at, #status, #result.ai_analysis.match_score',
            ExpressionAttributeNames={
                '#status': 'status',
                '#result': 'result'
            }
        )
        
        items = response.get('Items', [])
        
        # Calculate statistics
        total_analyses = len(items)
        completed_analyses = len([item for item in items if item.get('status') == 'completed'])
        
        match_scores = []
        for item in items:
            try:
                score = item.get('result', {}).get('ai_analysis', {}).get('match_score', 0)
                if isinstance(score, (int, float)) and 0 <= score <= 100:
                    match_scores.append(score)
            except:
                continue
        
        avg_match_score = sum(match_scores) / len(match_scores) if match_scores else 0
        
        stats = {
            'total_analyses': total_analyses,
            'completed_analyses': completed_analyses,
            'average_match_score': round(avg_match_score, 2),
            'score_distribution': {
                'excellent': len([s for s in match_scores if s >= 80]),
                'good': len([s for s in match_scores if 60 <= s < 80]),
                'fair': len([s for s in match_scores if 40 <= s < 60]),
                'poor': len([s for s in match_scores if s < 40])
            }
        }
        
        return create_response(200, stats)
        
    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")
        return create_response(500, {
            'error': 'Failed to retrieve statistics',
            'message': str(e)
        })

"""
Lambda function for user authentication using AWS Cognito
"""
import json
import os
import logging
import boto3
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any
from utils import create_response

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

# AWS clients
cognito_client = boto3.client('cognito-idp')
dynamodb = boto3.resource('dynamodb')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Handle user authentication requests
    """
    try:
        logger.info("Processing authentication request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Get path and method
        path = event.get('path', '')
        method = event.get('httpMethod', 'GET')
        
        # Route to appropriate handler
        if '/auth/register' in path and method == 'POST':
            return handle_register(event)
        elif '/auth/login' in path and method == 'POST':
            return handle_login(event)
        elif '/auth/profile' in path and method == 'GET':
            return handle_get_profile(event)
        else:
            return create_response(404, {'error': 'Endpoint not found'})
        
    except Exception as e:
        logger.error(f"Error in auth handler: {str(e)}")
        return create_response(500, {
            'error': 'Authentication service error',
            'message': str(e)
        })

def handle_register(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle user registration"""
    try:
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        # Validate required fields
        required_fields = ['email', 'password', 'name']
        for field in required_fields:
            if field not in body:
                return create_response(400, {'error': f'Missing required field: {field}'})
        
        email = body['email']
        password = body['password']
        name = body['name']
        company = body.get('company', '')
        
        # Validate email format
        if '@' not in email or '.' not in email:
            return create_response(400, {'error': 'Invalid email format'})
        
        # Validate password strength
        if len(password) < 8:
            return create_response(400, {'error': 'Password must be at least 8 characters'})
        
        user_pool_id = os.getenv('USER_POOL_ID')
        
        # Create user in Cognito
        try:
            response = cognito_client.admin_create_user(
                UserPoolId=user_pool_id,
                Username=email,
                UserAttributes=[
                    {'Name': 'email', 'Value': email},
                    {'Name': 'name', 'Value': name},
                    {'Name': 'email_verified', 'Value': 'true'}
                ],
                TemporaryPassword=password,
                MessageAction='SUPPRESS'  # Don't send welcome email
            )
            
            # Set permanent password
            cognito_client.admin_set_user_password(
                UserPoolId=user_pool_id,
                Username=email,
                Password=password,
                Permanent=True
            )
            
            user_id = response['User']['Username']
            
        except cognito_client.exceptions.UsernameExistsException:
            return create_response(409, {'error': 'User already exists'})
        except Exception as e:
            logger.error(f"Cognito error: {str(e)}")
            return create_response(500, {'error': 'Failed to create user account'})
        
        # Save user profile to DynamoDB
        try:
            users_table_name = os.getenv('USERS_TABLE')
            users_table = dynamodb.Table(users_table_name)
            
            user_profile = {
                'user_id': user_id,
                'email': email,
                'name': name,
                'company': company,
                'created_at': datetime.utcnow().isoformat(),
                'last_login': datetime.utcnow().isoformat(),
                'analysis_count': 0,
                'subscription_tier': 'free'
            }
            
            users_table.put_item(Item=user_profile)
            
        except Exception as e:
            logger.error(f"DynamoDB error: {str(e)}")
            # User created in Cognito but profile save failed
            # Could implement cleanup logic here
        
        response_data = {
            'user_id': user_id,
            'email': email,
            'name': name,
            'message': 'User registered successfully'
        }
        
        logger.info(f"User registered successfully: {email}")
        return create_response(201, response_data)
        
    except Exception as e:
        logger.error(f"Error in user registration: {str(e)}")
        return create_response(500, {
            'error': 'Registration failed',
            'message': str(e)
        })

def handle_login(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle user login"""
    try:
        # Parse request body
        if 'body' not in event:
            return create_response(400, {'error': 'Missing request body'})
        
        try:
            if isinstance(event['body'], str):
                body = json.loads(event['body'])
            else:
                body = event['body']
        except json.JSONDecodeError:
            return create_response(400, {'error': 'Invalid JSON in request body'})
        
        # Validate required fields
        email = body.get('email')
        password = body.get('password')
        
        if not email or not password:
            return create_response(400, {'error': 'Email and password are required'})
        
        user_pool_id = os.getenv('USER_POOL_ID')
        client_id = os.getenv('USER_POOL_CLIENT_ID')
        
        # Authenticate with Cognito
        try:
            response = cognito_client.admin_initiate_auth(
                UserPoolId=user_pool_id,
                ClientId=client_id,
                AuthFlow='ADMIN_NO_SRP_AUTH',
                AuthParameters={
                    'USERNAME': email,
                    'PASSWORD': password
                }
            )
            
            # Extract tokens
            auth_result = response['AuthenticationResult']
            access_token = auth_result['AccessToken']
            id_token = auth_result['IdToken']
            refresh_token = auth_result['RefreshToken']
            
            # Get user info
            user_info = cognito_client.get_user(AccessToken=access_token)
            user_attributes = {attr['Name']: attr['Value'] for attr in user_info['UserAttributes']}
            
            # Update last login in DynamoDB
            try:
                users_table_name = os.getenv('USERS_TABLE')
                users_table = dynamodb.Table(users_table_name)
                
                users_table.update_item(
                    Key={'user_id': user_info['Username']},
                    UpdateExpression='SET last_login = :timestamp',
                    ExpressionAttributeValues={
                        ':timestamp': datetime.utcnow().isoformat()
                    }
                )
            except Exception as e:
                logger.warning(f"Failed to update last login: {str(e)}")
            
            response_data = {
                'user_id': user_info['Username'],
                'email': user_attributes.get('email'),
                'name': user_attributes.get('name'),
                'access_token': access_token,
                'id_token': id_token,
                'refresh_token': refresh_token,
                'expires_in': auth_result['ExpiresIn'],
                'message': 'Login successful'
            }
            
            logger.info(f"User logged in successfully: {email}")
            return create_response(200, response_data)
            
        except cognito_client.exceptions.NotAuthorizedException:
            return create_response(401, {'error': 'Invalid email or password'})
        except cognito_client.exceptions.UserNotConfirmedException:
            return create_response(401, {'error': 'User account not confirmed'})
        except Exception as e:
            logger.error(f"Cognito authentication error: {str(e)}")
            return create_response(500, {'error': 'Authentication failed'})
        
    except Exception as e:
        logger.error(f"Error in user login: {str(e)}")
        return create_response(500, {
            'error': 'Login failed',
            'message': str(e)
        })

def handle_get_profile(event: Dict[str, Any]) -> Dict[str, Any]:
    """Get user profile information"""
    try:
        # Extract access token from Authorization header
        headers = event.get('headers', {})
        auth_header = headers.get('Authorization') or headers.get('authorization')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            return create_response(401, {'error': 'Missing or invalid authorization header'})
        
        access_token = auth_header.replace('Bearer ', '')
        
        # Validate token and get user info
        try:
            user_info = cognito_client.get_user(AccessToken=access_token)
            user_attributes = {attr['Name']: attr['Value'] for attr in user_info['UserAttributes']}
            user_id = user_info['Username']
            
        except cognito_client.exceptions.NotAuthorizedException:
            return create_response(401, {'error': 'Invalid or expired token'})
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return create_response(401, {'error': 'Token validation failed'})
        
        # Get extended profile from DynamoDB
        try:
            users_table_name = os.getenv('USERS_TABLE')
            users_table = dynamodb.Table(users_table_name)
            
            response = users_table.get_item(Key={'user_id': user_id})
            
            if 'Item' in response:
                profile = response['Item']
            else:
                # Create basic profile if not exists
                profile = {
                    'user_id': user_id,
                    'email': user_attributes.get('email'),
                    'name': user_attributes.get('name'),
                    'created_at': datetime.utcnow().isoformat(),
                    'analysis_count': 0,
                    'subscription_tier': 'free'
                }
                users_table.put_item(Item=profile)
            
        except Exception as e:
            logger.warning(f"Failed to get profile from DynamoDB: {str(e)}")
            # Fallback to Cognito data only
            profile = {
                'user_id': user_id,
                'email': user_attributes.get('email'),
                'name': user_attributes.get('name'),
                'analysis_count': 0,
                'subscription_tier': 'free'
            }
        
        # Remove sensitive information
        safe_profile = {k: v for k, v in profile.items() if k not in ['password', 'tokens']}
        
        logger.info(f"Profile retrieved for user: {user_id}")
        return create_response(200, safe_profile)
        
    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        return create_response(500, {
            'error': 'Failed to get profile',
            'message': str(e)
        })

def validate_token(access_token: str) -> Dict[str, Any]:
    """Validate Cognito access token and return user info"""
    try:
        user_info = cognito_client.get_user(AccessToken=access_token)
        user_attributes = {attr['Name']: attr['Value'] for attr in user_info['UserAttributes']}
        
        return {
            'valid': True,
            'user_id': user_info['Username'],
            'email': user_attributes.get('email'),
            'name': user_attributes.get('name')
        }
        
    except Exception as e:
        logger.error(f"Token validation failed: {str(e)}")
        return {'valid': False, 'error': str(e)}

"""
Shared utilities for Resume Analyzer Lambda functions
"""
import json
import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import boto3
from botocore.exceptions import ClientError

# Configure logging
logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

# AWS clients
s3_client = boto3.client('s3')
dynamodb = boto3.resource('dynamodb')
textract_client = boto3.client('textract')
comprehend_client = boto3.client('comprehend')
bedrock_client = boto3.client('bedrock-runtime', region_name=os.getenv('BEDROCK_REGION', 'us-east-2'))

def create_response(status_code: int, body: Dict[Any, Any], headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Create a standardized API response"""
    default_headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    }
    
    if headers:
        default_headers.update(headers)
    
    return {
        'statusCode': status_code,
        'headers': default_headers,
        'body': json.dumps(body, default=str)
    }

def generate_analysis_id() -> str:
    """Generate a unique analysis ID"""
    return str(uuid.uuid4())

def get_current_timestamp() -> str:
    """Get current timestamp in ISO format"""
    return datetime.utcnow().isoformat()

def get_ttl_timestamp(days: int = 7) -> int:
    """Get TTL timestamp for DynamoDB (Unix timestamp)"""
    return int((datetime.utcnow() + timedelta(days=days)).timestamp())

def extract_text_from_file(bucket: str, key: str, file_type: str) -> str:
    """Extract text from uploaded file using AWS Textract"""
    try:
        logger.info(f"Extracting text from {file_type} file: {key}")
        
        if file_type.lower() in ['pdf', 'png', 'jpg', 'jpeg']:
            # Use Textract for PDF and image files
            response = textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            # Extract text from Textract response
            text_blocks = []
            for block in response.get('Blocks', []):
                if block['BlockType'] == 'LINE':
                    text_blocks.append(block['Text'])
            
            return '\n'.join(text_blocks)
        
        else:
            # For other file types, download and process locally
            obj = s3_client.get_object(Bucket=bucket, Key=key)
            file_content = obj['Body'].read()
            
            if file_type.lower() == 'txt':
                return file_content.decode('utf-8')
            elif file_type.lower() in ['doc', 'docx']:
                # Handle DOCX files (would need python-docx in Lambda layer)
                return "DOCX processing not implemented yet"
            else:
                return "Unsupported file type"
                
    except Exception as e:
        logger.error(f"Error extracting text from file: {str(e)}")
        raise

def analyze_text_with_comprehend(text: str) -> Dict[str, Any]:
    """Analyze text using AWS Comprehend"""
    try:
        logger.info("Analyzing text with AWS Comprehend")
        
        # Truncate text if too long (Comprehend has limits)
        max_length = 5000
        if len(text) > max_length:
            text = text[:max_length]
        
        # Detect sentiment
        sentiment_response = comprehend_client.detect_sentiment(
            Text=text,
            LanguageCode='en'
        )
        
        # Detect key phrases
        phrases_response = comprehend_client.detect_key_phrases(
            Text=text,
            LanguageCode='en'
        )
        
        # Detect entities
        entities_response = comprehend_client.detect_entities(
            Text=text,
            LanguageCode='en'
        )
        
        return {
            'sentiment': sentiment_response.get('Sentiment'),
            'sentiment_scores': sentiment_response.get('SentimentScore'),
            'key_phrases': [phrase['Text'] for phrase in phrases_response.get('KeyPhrases', [])],
            'entities': [
                {
                    'text': entity['Text'],
                    'type': entity['Type'],
                    'confidence': entity['Score']
                }
                for entity in entities_response.get('Entities', [])
            ]
        }
        
    except Exception as e:
        logger.error(f"Error analyzing text with Comprehend: {str(e)}")
        return {}

def analyze_with_bedrock(resume_text: str, job_description: str) -> Dict[str, Any]:
    """Analyze resume against job description using AWS Bedrock (Claude)"""
    try:
        logger.info("Analyzing with AWS Bedrock (Claude)")
        
        prompt = f"""
        You are an expert HR analyst and career counselor. Analyze the following resume against the job description and provide a comprehensive analysis.

        RESUME:
        {resume_text}

        JOB DESCRIPTION:
        {job_description}

        Please provide a detailed analysis in the following JSON format:
        {{
            "match_score": <number between 0-100>,
            "overall_assessment": "<brief overall assessment>",
            "strengths": [
                "<strength 1>",
                "<strength 2>",
                "<strength 3>"
            ],
            "missing_skills": [
                "<missing skill 1>",
                "<missing skill 2>",
                "<missing skill 3>"
            ],
            "recommendations": [
                "<recommendation 1>",
                "<recommendation 2>",
                "<recommendation 3>"
            ],
            "skill_matches": [
                {{
                    "skill": "<skill name>",
                    "found_in_resume": true/false,
                    "importance": "high/medium/low"
                }}
            ],
            "experience_analysis": {{
                "years_experience": <number>,
                "relevant_experience": "<assessment>",
                "career_level": "<junior/mid/senior/executive>"
            }},
            "education_analysis": {{
                "meets_requirements": true/false,
                "education_level": "<level>",
                "relevant_field": true/false
            }},
            "improvement_areas": [
                "<area 1>",
                "<area 2>",
                "<area 3>"
            ]
        }}

        Ensure the response is valid JSON only, no additional text.
        """
        
        # Prepare the request for Claude
        request_body = {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": 4000,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }
        
        # Call Bedrock
        response = bedrock_client.invoke_model(
            modelId="anthropic.claude-3-sonnet-20240229-v1:0",
            body=json.dumps(request_body)
        )
        
        # Parse response
        response_body = json.loads(response['body'].read())
        analysis_text = response_body['content'][0]['text']
        
        # Parse the JSON response from Claude
        try:
            analysis_result = json.loads(analysis_text)
            return analysis_result
        except json.JSONDecodeError:
            logger.error("Failed to parse Claude response as JSON")
            return {
                "match_score": 0,
                "overall_assessment": "Analysis failed - invalid response format",
                "error": "Failed to parse AI response"
            }
        
    except Exception as e:
        logger.error(f"Error analyzing with Bedrock: {str(e)}")
        return {
            "match_score": 0,
            "overall_assessment": "Analysis failed due to technical error",
            "error": str(e)
        }

def save_analysis_result(analysis_id: str, result: Dict[str, Any]) -> bool:
    """Save analysis result to DynamoDB"""
    try:
        table_name = os.getenv('RESULTS_TABLE')
        table = dynamodb.Table(table_name)
        
        item = {
            'analysis_id': analysis_id,
            'created_at': get_current_timestamp(),
            'ttl': get_ttl_timestamp(),
            'result': result,
            'status': 'completed'
        }
        
        table.put_item(Item=item)
        logger.info(f"Saved analysis result for ID: {analysis_id}")
        return True
        
    except Exception as e:
        logger.error(f"Error saving analysis result: {str(e)}")
        return False

def get_analysis_result(analysis_id: str) -> Optional[Dict[str, Any]]:
    """Get analysis result from DynamoDB"""
    try:
        table_name = os.getenv('RESULTS_TABLE')
        table = dynamodb.Table(table_name)
        
        response = table.get_item(Key={'analysis_id': analysis_id})
        
        if 'Item' in response:
            return response['Item']
        else:
            return None
            
    except Exception as e:
        logger.error(f"Error getting analysis result: {str(e)}")
        return None

def validate_file_type(filename: str) -> bool:
    """Validate if file type is supported"""
    allowed_extensions = ['.pdf', '.docx', '.doc', '.txt', '.png', '.jpg', '.jpeg']
    file_extension = os.path.splitext(filename.lower())[1]
    return file_extension in allowed_extensions

def get_file_type(filename: str) -> str:
    """Get file type from filename"""
    return os.path.splitext(filename.lower())[1][1:]  # Remove the dot

"""
Lambda function for health checks and API status
"""
import json
import os
import logging
from datetime import datetime
from typing import Dict, Any
import boto3
from botocore.exceptions import ClientError
from utils import create_response

logger = logging.getLogger()
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Health check endpoint
    Returns system status and connectivity information
    """
    try:
        logger.info("Processing health check request")
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return create_response(200, {'message': 'CORS preflight'})
        
        # Perform health checks
        health_status = perform_health_checks()
        
        # Determine overall status
        overall_status = 'healthy' if all(
            check['status'] == 'healthy' for check in health_status['checks'].values()
        ) else 'unhealthy'
        
        response_data = {
            'status': overall_status,
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'environment': os.getenv('Environment', 'dev'),
            'region': os.getenv('AWS_REGION', 'unknown'),
            'function_name': context.function_name if context else 'unknown',
            'function_version': context.function_version if context else 'unknown',
            'checks': health_status['checks'],
            'system_info': {
                'python_version': '3.12',
                'lambda_runtime': 'AWS Lambda',
                'memory_limit': context.memory_limit_in_mb if context else 'unknown',
                'remaining_time': context.get_remaining_time_in_millis() if context else 'unknown'
            }
        }
        
        # Return appropriate status code
        status_code = 200 if overall_status == 'healthy' else 503
        
        logger.info(f"Health check completed with status: {overall_status}")
        return create_response(status_code, response_data)
        
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        return create_response(500, {
            'status': 'error',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e),
            'message': 'Health check failed due to internal error'
        })

def perform_health_checks() -> Dict[str, Any]:
    """Perform various health checks"""
    checks = {}
    
    # Check S3 connectivity
    checks['s3'] = check_s3_connectivity()
    
    # Check DynamoDB connectivity
    checks['dynamodb'] = check_dynamodb_connectivity()
    
    # Check Bedrock connectivity
    checks['bedrock'] = check_bedrock_connectivity()
    
    # Check Textract connectivity
    checks['textract'] = check_textract_connectivity()
    
    # Check Comprehend connectivity
    checks['comprehend'] = check_comprehend_connectivity()
    
    # Check environment variables
    checks['environment'] = check_environment_variables()
    
    return {'checks': checks}

def check_s3_connectivity() -> Dict[str, Any]:
    """Check S3 service connectivity"""
    try:
        s3_client = boto3.client('s3')
        bucket_name = os.getenv('UPLOAD_BUCKET')
        
        if not bucket_name:
            return {
                'status': 'unhealthy',
                'message': 'UPLOAD_BUCKET environment variable not set',
                'service': 'S3'
            }
        
        # Try to head the bucket
        s3_client.head_bucket(Bucket=bucket_name)
        
        return {
            'status': 'healthy',
            'message': 'S3 connectivity verified',
            'service': 'S3',
            'bucket': bucket_name
        }
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        return {
            'status': 'unhealthy',
            'message': f'S3 connectivity failed: {error_code}',
            'service': 'S3',
            'error': str(e)
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'message': f'S3 connectivity check failed: {str(e)}',
            'service': 'S3'
        }

def check_dynamodb_connectivity() -> Dict[str, Any]:
    """Check DynamoDB service connectivity"""
    try:
        dynamodb = boto3.resource('dynamodb')
        table_name = os.getenv('RESULTS_TABLE')
        
        if not table_name:
            return {
                'status': 'unhealthy',
                'message': 'RESULTS_TABLE environment variable not set',
                'service': 'DynamoDB'
            }
        
        # Try to describe the table
        table = dynamodb.Table(table_name)
        table.load()  # This will raise an exception if table doesn't exist
        
        return {
            'status': 'healthy',
            'message': 'DynamoDB connectivity verified',
            'service': 'DynamoDB',
            'table': table_name,
            'table_status': table.table_status
        }
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        return {
            'status': 'unhealthy',
            'message': f'DynamoDB connectivity failed: {error_code}',
            'service': 'DynamoDB',
            'error': str(e)
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'message': f'DynamoDB connectivity check failed: {str(e)}',
            'service': 'DynamoDB'
        }

def check_bedrock_connectivity() -> Dict[str, Any]:
    """Check AWS Bedrock service connectivity"""
    try:
        bedrock_client = boto3.client('bedrock-runtime', region_name=os.getenv('BEDROCK_REGION', 'us-east-2'))
        
        # Try to list foundation models (this is a lightweight check)
        bedrock_models_client = boto3.client('bedrock', region_name=os.getenv('BEDROCK_REGION', 'us-east-2'))
        response = bedrock_models_client.list_foundation_models(
            byProvider='anthropic'
        )
        
        claude_models = [model for model in response.get('modelSummaries', []) 
                        if 'claude' in model.get('modelId', '').lower()]
        
        return {
            'status': 'healthy',
            'message': 'Bedrock connectivity verified',
            'service': 'Bedrock',
            'region': os.getenv('BEDROCK_REGION', 'us-east-2'),
            'available_claude_models': len(claude_models)
        }
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        return {
            'status': 'unhealthy',
            'message': f'Bedrock connectivity failed: {error_code}',
            'service': 'Bedrock',
            'error': str(e)
        }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'message': f'Bedrock connectivity check failed: {str(e)}',
            'service': 'Bedrock'
        }

def check_textract_connectivity() -> Dict[str, Any]:
    """Check AWS Textract service connectivity"""
    try:
        textract_client = boto3.client('textract')
        
        # This is a simple check - we're not actually processing a document
        # Just verifying the client can be created and has proper permissions
        return {
            'status': 'healthy',
            'message': 'Textract service accessible',
            'service': 'Textract'
        }
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'message': f'Textract connectivity check failed: {str(e)}',
            'service': 'Textract'
        }

def check_comprehend_connectivity() -> Dict[str, Any]:
    """Check AWS Comprehend service connectivity"""
    try:
        comprehend_client = boto3.client('comprehend')
        
        # This is a simple check - we're not actually processing text
        # Just verifying the client can be created and has proper permissions
        return {
            'status': 'healthy',
            'message': 'Comprehend service accessible',
            'service': 'Comprehend'
        }
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'message': f'Comprehend connectivity check failed: {str(e)}',
            'service': 'Comprehend'
        }

def check_environment_variables() -> Dict[str, Any]:
    """Check required environment variables"""
    required_vars = [
        'UPLOAD_BUCKET',
        'RESULTS_TABLE',
        'BEDROCK_REGION'
    ]
    
    missing_vars = []
    present_vars = []
    
    for var in required_vars:
        if os.getenv(var):
            present_vars.append(var)
        else:
            missing_vars.append(var)
    
    if missing_vars:
        return {
            'status': 'unhealthy',
            'message': f'Missing environment variables: {", ".join(missing_vars)}',
            'service': 'Environment',
            'missing_variables': missing_vars,
            'present_variables': present_vars
        }
    else:
        return {
            'status': 'healthy',
            'message': 'All required environment variables present',
            'service': 'Environment',
            'variables_checked': required_vars
        }

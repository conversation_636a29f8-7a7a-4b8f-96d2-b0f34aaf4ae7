#!/usr/bin/env python3
"""
Enhanced deployment script for the Resume Analyzer Lambda function
with improved Gemini prompts and comprehensive analysis capabilities.
"""

import zipfile
import os
import boto3
import json
from botocore.exceptions import ClientError

# Configuration
LAMBDA_FUNCTION_NAME = "resume-analyzer"
REGION = "us-east-2"  # Chicago region

def create_deployment_package():
    """Create a deployment package with all necessary files"""
    print("📦 Creating deployment package...")
    
    # Files to include in the deployment
    files_to_include = [
        'lambda_function.py',
        'utils/bedrock_client.py',
        'utils/textract_client.py', 
        'utils/comprehend_client.py',
        'utils/s3_client.py',
        'utils/__init__.py'
    ]
    
    # Create deployment zip
    with zipfile.ZipFile('deployment.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in files_to_include:
            if os.path.exists(file_path):
                zipf.write(file_path, file_path)
                print(f"✅ Added {file_path}")
            else:
                print(f"⚠️  Warning: {file_path} not found")
    
    print("✅ Deployment package created: deployment.zip")
    return 'deployment.zip'

def update_lambda_function(zip_file_path):
    """Update the Lambda function with enhanced code"""
    print(f"🚀 Updating Lambda function: {LAMBDA_FUNCTION_NAME}")
    
    try:
        # Initialize Lambda client
        lambda_client = boto3.client('lambda', region_name=REGION)
        
        # Read the deployment package
        with open(zip_file_path, 'rb') as zip_file:
            zip_content = zip_file.read()
        
        # Update function code
        response = lambda_client.update_function_code(
            FunctionName=LAMBDA_FUNCTION_NAME,
            ZipFile=zip_content
        )
        
        print("✅ Lambda function updated successfully!")
        print(f"📋 Function ARN: {response['FunctionArn']}")
        print(f"📋 Last Modified: {response['LastModified']}")
        print(f"📋 Code Size: {response['CodeSize']} bytes")
        
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_message = e.response['Error']['Message']
        print(f"❌ Error updating Lambda function: {error_code} - {error_message}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def update_environment_variables():
    """Update Lambda environment variables if needed"""
    print("🔧 Updating environment variables...")
    
    try:
        lambda_client = boto3.client('lambda', region_name=REGION)
        
        # Enhanced environment variables
        env_vars = {
            'ENHANCED_ANALYSIS': 'true',
            'GEMINI_MODEL': 'gemini-1.5-pro',
            'ANALYSIS_VERSION': '2.0',
            'FIELD_COMPATIBILITY_CHECK': 'enabled',
            'DOCUMENT_VALIDATION': 'enabled',
            'COMPREHENSIVE_SCORING': 'enabled'
        }
        
        response = lambda_client.update_function_configuration(
            FunctionName=LAMBDA_FUNCTION_NAME,
            Environment={
                'Variables': env_vars
            }
        )
        
        print("✅ Environment variables updated!")
        return True
        
    except ClientError as e:
        print(f"⚠️  Warning: Could not update environment variables: {e}")
        return False

def test_enhanced_analysis():
    """Test the enhanced analysis with a sample request"""
    print("🧪 Testing enhanced analysis...")
    
    try:
        lambda_client = boto3.client('lambda', region_name=REGION)
        
        # Sample test payload
        test_payload = {
            "analysis_id": "test_enhanced_analysis",
            "resume_text": "John Smith, Software Engineer with 5 years experience in Python, JavaScript, React, AWS Lambda, and machine learning.",
            "job_description": "Senior Software Engineer position requiring 3+ years experience in Python, JavaScript, React, AWS services, and data analysis skills."
        }
        
        response = lambda_client.invoke(
            FunctionName=LAMBDA_FUNCTION_NAME,
            InvocationType='RequestResponse',
            Payload=json.dumps(test_payload)
        )
        
        # Parse response
        response_payload = json.loads(response['Payload'].read())
        
        if response['StatusCode'] == 200:
            print("✅ Enhanced analysis test successful!")
            
            # Check for new fields
            if 'body' in response_payload:
                body = json.loads(response_payload['body'])
                analysis = body.get('analysis', {})
                
                new_fields = [
                    'field_compatibility',
                    'document_quality', 
                    'salary_expectation',
                    'red_flags',
                    'competitive_advantages'
                ]
                
                print("🔍 Checking for enhanced fields:")
                for field in new_fields:
                    if field in analysis:
                        print(f"  ✅ {field}: Present")
                    else:
                        print(f"  ❌ {field}: Missing")
                        
                print(f"📊 Match Percentage: {analysis.get('match_percentage', 'N/A')}%")
                print(f"🎯 Field Compatibility: {analysis.get('field_compatibility', 'N/A')}")
                
        else:
            print(f"❌ Test failed with status code: {response['StatusCode']}")
            print(f"Response: {response_payload}")
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")

def main():
    """Main deployment function"""
    print("🚀 Starting Enhanced Resume Analyzer Deployment")
    print("=" * 50)
    
    # Step 1: Create deployment package
    zip_file = create_deployment_package()
    
    # Step 2: Update Lambda function
    if update_lambda_function(zip_file):
        print("\n🔧 Lambda function updated successfully!")
        
        # Step 3: Update environment variables
        update_environment_variables()
        
        # Step 4: Test the enhanced analysis
        print("\n🧪 Testing enhanced analysis...")
        test_enhanced_analysis()
        
        print("\n🎉 Enhanced deployment completed successfully!")
        print("\n📋 Enhanced Features Deployed:")
        print("  ✅ Comprehensive field compatibility detection")
        print("  ✅ Document quality validation")
        print("  ✅ Enhanced keyword matching with ratios")
        print("  ✅ Salary expectation analysis")
        print("  ✅ Red flags identification")
        print("  ✅ Competitive advantages detection")
        print("  ✅ Detailed experience and education gap analysis")
        print("  ✅ Edge case handling (field mismatches, level mismatches)")
        print("  ✅ Comprehensive scoring with examples")
        
    else:
        print("❌ Deployment failed!")
    
    # Cleanup
    if os.path.exists(zip_file):
        os.remove(zip_file)
        print(f"🧹 Cleaned up {zip_file}")

if __name__ == "__main__":
    main()

# This file is auto generated by SAM CLI build command

[function_build_definitions.18004041-3950-4617-8534-252d9a3b893a]
codeuri = "/Users/<USER>/Desktop/(resume checker) aws hackathon/backend/src"
runtime = "python3.12"
architecture = "x86_64"
handler = "upload_handler.lambda_handler"
manifest_hash = ""
packagetype = "Zip"
functions = ["FileUploadFunction", "AnalysisFunction", "ResultsFunction", "HealthCheckFunction"]

[layer_build_definitions]

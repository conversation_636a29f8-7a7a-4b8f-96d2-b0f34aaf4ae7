AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'Resume-to-Job-Match Analyzer AWS Lambda Hackathon Project - Serverless
  Resume Analysis with AI

  '
Globals:
  Function:
    Timeout: 300
    MemorySize: 1024
    Runtime: python3.12
    Environment:
      Variables:
        CORS_ORIGIN: '*'
        LOG_LEVEL: INFO
Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
    - dev
    - staging
    - prod
    Description: Environment name
Resources:
  ResumeUploadBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName:
        Fn::Sub: resume-analyzer-uploads-${Environment}-${AWS::AccountId}
      CorsConfiguration:
        CorsRules:
        - AllowedHeaders:
          - '*'
          AllowedMethods:
          - GET
          - PUT
          - POST
          - DELETE
          - HEAD
          AllowedOrigins:
          - '*'
          MaxAge: 3000
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
        - Id: DeleteOldFiles
          Status: Enabled
          ExpirationInDays: 7
  AnalysisResultsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName:
        Fn::Sub: resume-analysis-results-${Environment}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
      - AttributeName: analysis_id
        AttributeType: S
      - AttributeName: created_at
        AttributeType: S
      KeySchema:
      - AttributeName: analysis_id
        KeyType: HASH
      GlobalSecondaryIndexes:
      - IndexName: CreatedAtIndex
        KeySchema:
        - AttributeName: created_at
          KeyType: HASH
        Projection:
          ProjectionType: ALL
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
  ResumeAnalyzerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName:
        Ref: Environment
      Cors:
        AllowMethods: '''GET,POST,PUT,DELETE,OPTIONS'''
        AllowHeaders: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
        AllowOrigin: '''*'''
      GatewayResponses:
        DEFAULT_4XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: '''*'''
              Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
        DEFAULT_5XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: '''*'''
              Access-Control-Allow-Headers: '''Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'''
  FileUploadFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: resume-analyzer-upload-${Environment}
      CodeUri: FileUploadFunction
      Handler: upload_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET:
            Ref: ResumeUploadBucket
          RESULTS_TABLE:
            Ref: AnalysisResultsTable
      Policies:
      - S3WritePolicy:
          BucketName:
            Ref: ResumeUploadBucket
      - DynamoDBWritePolicy:
          TableName:
            Ref: AnalysisResultsTable
      Events:
        UploadApi:
          Type: Api
          Properties:
            RestApiId:
              Ref: ResumeAnalyzerApi
            Path: /upload
            Method: post
    Metadata:
      SamResourceId: FileUploadFunction
  AnalysisFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: resume-analyzer-analysis-${Environment}
      CodeUri: AnalysisFunction
      Handler: analysis_handler.lambda_handler
      Environment:
        Variables:
          UPLOAD_BUCKET:
            Ref: ResumeUploadBucket
          RESULTS_TABLE:
            Ref: AnalysisResultsTable
          BEDROCK_REGION:
            Ref: AWS::Region
      Policies:
      - S3ReadPolicy:
          BucketName:
            Ref: ResumeUploadBucket
      - DynamoDBWritePolicy:
          TableName:
            Ref: AnalysisResultsTable
      - Statement:
        - Effect: Allow
          Action:
          - bedrock:InvokeModel
          - bedrock:InvokeModelWithResponseStream
          Resource:
          - Fn::Sub: arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0
          - Fn::Sub: arn:aws:bedrock:${AWS::Region}::foundation-model/anthropic.claude-3-haiku-20240307-v1:0
      - Statement:
        - Effect: Allow
          Action:
          - textract:DetectDocumentText
          - textract:AnalyzeDocument
          Resource: '*'
      - Statement:
        - Effect: Allow
          Action:
          - comprehend:DetectSentiment
          - comprehend:DetectKeyPhrases
          - comprehend:DetectEntities
          Resource: '*'
      Events:
        AnalysisApi:
          Type: Api
          Properties:
            RestApiId:
              Ref: ResumeAnalyzerApi
            Path: /analyze
            Method: post
    Metadata:
      SamResourceId: AnalysisFunction
  ResultsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: resume-analyzer-results-${Environment}
      CodeUri: ResultsFunction
      Handler: results_handler.lambda_handler
      Environment:
        Variables:
          RESULTS_TABLE:
            Ref: AnalysisResultsTable
      Policies:
      - DynamoDBReadPolicy:
          TableName:
            Ref: AnalysisResultsTable
      Events:
        GetResultsApi:
          Type: Api
          Properties:
            RestApiId:
              Ref: ResumeAnalyzerApi
            Path: /results/{analysis_id}
            Method: get
        ListResultsApi:
          Type: Api
          Properties:
            RestApiId:
              Ref: ResumeAnalyzerApi
            Path: /results
            Method: get
    Metadata:
      SamResourceId: ResultsFunction
  HealthCheckFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: resume-analyzer-health-${Environment}
      CodeUri: HealthCheckFunction
      Handler: health_handler.lambda_handler
      Events:
        HealthApi:
          Type: Api
          Properties:
            RestApiId:
              Ref: ResumeAnalyzerApi
            Path: /health
            Method: get
    Metadata:
      SamResourceId: HealthCheckFunction
Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL
    Value:
      Fn::Sub: https://${ResumeAnalyzerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-ApiUrl
  UploadBucketName:
    Description: S3 bucket for file uploads
    Value:
      Ref: ResumeUploadBucket
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-UploadBucket
  ResultsTableName:
    Description: DynamoDB table for analysis results
    Value:
      Ref: AnalysisResultsTable
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-ResultsTable
  Region:
    Description: AWS Region
    Value:
      Ref: AWS::Region
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-Region

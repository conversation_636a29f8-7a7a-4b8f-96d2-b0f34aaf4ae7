{"pagination": {"ListDbInstances": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDbParameterGroups": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDbClusters": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDbInstancesForCluster": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}}}
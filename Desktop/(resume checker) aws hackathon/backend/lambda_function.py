import json
import boto3
import base64
import os
from typing import Dict, Any
import logging
from utils.resume_parser import ResumeParser
from utils.bedrock_client import BedrockClient
from utils.s3_handler import S3Handler

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
s3_handler = S3Handler()
bedrock_client = BedrockClient()
resume_parser = ResumeParser()

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Main Lambda handler for resume-to-job matching analysis
    """
    try:
        # CORS headers
        headers = {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
            'Access-Control-Allow-Methods': 'GET,POST,OPTIONS'
        }
        
        # Handle preflight OPTIONS request
        if event.get('httpMethod') == 'OPTIONS':
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({'message': 'CORS preflight'})
            }
        
        # Parse the request
        http_method = event.get('httpMethod', '')
        path = event.get('path', '')
        
        if http_method == 'GET' and path == '/health':
            return handle_health_check(headers)
        
        elif http_method == 'POST' and path == '/analyze':
            return handle_analyze_request(event, headers)
        
        else:
            return {
                'statusCode': 404,
                'headers': headers,
                'body': json.dumps({'error': 'Endpoint not found'})
            }
            
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': 'Internal server error'})
        }

def handle_health_check(headers: Dict[str, str]) -> Dict[str, Any]:
    """Handle health check endpoint"""
    return {
        'statusCode': 200,
        'headers': headers,
        'body': json.dumps({
            'status': 'healthy',
            'service': 'resume-job-matcher',
            'version': '1.0.0'
        })
    }

def handle_analyze_request(event: Dict[str, Any], headers: Dict[str, str]) -> Dict[str, Any]:
    """Handle resume analysis request"""
    try:
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Extract data
        resume_file = body.get('resume_file')  # Base64 encoded file
        resume_text = body.get('resume_text')  # Direct text input
        job_description = body.get('job_description', '')
        file_type = body.get('file_type', 'text')  # 'pdf' or 'text'
        
        if not job_description:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Job description is required'})
            }
        
        # Process resume
        if resume_file:
            # Handle file upload
            resume_content = process_uploaded_file(resume_file, file_type)
        elif resume_text:
            # Handle direct text input
            resume_content = resume_text
        else:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Resume file or text is required'})
            }
        
        # Analyze with Bedrock (Claude)
        analysis_result = bedrock_client.analyze_resume_job_match(
            resume_content, 
            job_description
        )
        
        # Log the analysis (optional - for analytics)
        log_analysis_result(resume_content, job_description, analysis_result)
        
        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps({
                'success': True,
                'analysis': analysis_result
            })
        }
        
    except Exception as e:
        logger.error(f"Error in analyze request: {str(e)}")
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': f'Analysis failed: {str(e)}'})
        }

def process_uploaded_file(file_data: str, file_type: str) -> str:
    """Process uploaded file and extract text content"""
    try:
        # Decode base64 file
        file_bytes = base64.b64decode(file_data)
        
        # Save to S3 (optional - for audit trail)
        file_key = s3_handler.save_file(file_bytes, file_type)
        
        # Extract text based on file type
        if file_type.lower() == 'pdf':
            text_content = resume_parser.extract_text_from_pdf(file_bytes)
        else:
            text_content = file_bytes.decode('utf-8')
        
        return text_content
        
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise Exception(f"Failed to process uploaded file: {str(e)}")

def log_analysis_result(resume_content: str, job_description: str, analysis: Dict[str, Any]) -> None:
    """Log analysis result for analytics (optional)"""
    try:
        # This could save to DynamoDB for analytics
        # For now, just log to CloudWatch
        logger.info(f"Analysis completed - Match Score: {analysis.get('match_percentage', 'N/A')}")
    except Exception as e:
        logger.warning(f"Failed to log analysis result: {str(e)}")
